package com.example.tikoppij

import android.app.Application
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.data.FavoriteRepository
import com.example.tikoppij.data.FavoriteRepositoryImpl
import com.example.tikoppij.data.HistoryRepository
import com.example.tikoppij.data.HistoryRepositoryImpl
import com.example.tikoppij.data.UserPreferencesRepository
import com.example.tikoppij.data.UserPreferencesRepositoryImpl
import com.example.tikoppij.utils.PerformanceMonitor

/**
 * Tikoppij应用程序主类
 * 
 * 这个类是整个应用的入口点，负责全局级别的初始化工作。
 * 为了优化应用启动性能，大部分的初始化工作已经委托给了App Startup库来处理，
 * 这里只保留最核心和必需的初始化逻辑。
 * 
 * 设计原则：
 * - 最小化初始化工作：只初始化最核心的组件
 * - 利用App Startup：复杂的初始化工作交给专门的Initializer处理
 * - 全局访问：为应用提供全局访问的关键服务实例
 * - 性能监控：记录关键的性能指标
 * 
 * <AUTHOR>
 * @since 1.0
 */
@UnstableApi
class MyApplication : Application() {
    
    /**
     * 全局访问的单例对象
     * 
     * 这些对象在整个应用生命周期中需要被多个组件访问，
     * 通过Application类提供全局访问点，避免重复创建。
     */
    companion object {
        /**
         * 用户偏好设置仓库
         * 
         * 负责管理用户的各种偏好设置，如主题选择、播放设置等。
         * 使用DataStore作为底层存储，提供协程友好的异步访问。
         */
        lateinit var userPreferencesRepository: UserPreferencesRepository
            private set
            
        /**
         * 收藏管理仓库
         * 
         * 负责管理用户收藏的视频列表，提供增删改查功能。
         * 使用DataStore存储收藏数据，支持持久化。
         */
        lateinit var favoriteRepository: FavoriteRepository
            private set
            
        /**
         * 历史记录管理仓库
         * 
         * 负责管理用户的观看历史记录，包括观看时间和进度。
         * 使用DataStore存储历史数据，自动限制记录数量。
         */
        lateinit var historyRepository: HistoryRepository
            private set
    }
    
    /**
     * 应用程序创建时的回调
     * 
     * 在这里进行最核心的初始化工作。复杂的初始化已经通过
     * App Startup在后台并行处理，这里只关注最重要的组件。
     */
    override fun onCreate() {
        super.onCreate()
        
        // 初始化用户偏好设置仓库
        // 这个仓库会被多个ViewModel使用，所以在这里提前初始化
        userPreferencesRepository = UserPreferencesRepositoryImpl.getInstance(this)
        
        // 初始化收藏管理仓库
        // 用于视频收藏功能，在播放页面和个人中心都会使用
        favoriteRepository = FavoriteRepositoryImpl.getInstance(this)
        
        // 初始化历史记录管理仓库
        // 用于记录用户观看历史，支持继续观看功能
        historyRepository = HistoryRepositoryImpl.getInstance(this)
        
        // 记录应用onCreate完成时间点
        // 用于性能分析和优化，帮助监控应用启动速度
        PerformanceMonitor.recordTimePoint("应用onCreate完成")
        
        // 注意：其他初始化工作（如网络配置、缓存初始化等）
        // 已经通过App Startup在initializer包中的AppInitializer完成
    }
} 