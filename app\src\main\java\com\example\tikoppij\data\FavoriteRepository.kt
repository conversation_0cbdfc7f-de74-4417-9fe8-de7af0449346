package com.example.tikoppij.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.example.tikoppij.model.FavoriteModel
import com.example.tikoppij.model.VideoModel
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * 收藏管理Repository接口
 */
interface FavoriteRepository {
    /**
     * 获取收藏列表
     */
    fun getFavoriteList(): Flow<List<FavoriteModel>>
    
    /**
     * 添加收藏
     */
    suspend fun addFavorite(video: VideoModel)
    
    /**
     * 移除收藏
     */
    suspend fun removeFavorite(videoId: String)
    
    /**
     * 检查是否已收藏
     */
    fun isFavorite(videoId: String): Flow<Boolean>
    
    /**
     * 清空收藏列表
     */
    suspend fun clearFavorites()
}

/**
 * 收藏管理Repository实现
 */
class FavoriteRepositoryImpl private constructor(
    context: Context
) : FavoriteRepository {
    
    private val appContext = context.applicationContext
    
    companion object {
        @Volatile
        private var INSTANCE: FavoriteRepositoryImpl? = null
        
        fun getInstance(context: Context): FavoriteRepositoryImpl {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FavoriteRepositoryImpl(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // DataStore扩展
        private val Context.favoriteDataStore: DataStore<Preferences> by preferencesDataStore(name = "favorites")
        
        // PreferencesKeys
        private val FAVORITE_LIST_KEY = stringPreferencesKey("favorite_list")
    }
    
    private val gson = Gson()
    private val favoriteListType = object : TypeToken<List<FavoriteModel>>() {}.type
    
    override fun getFavoriteList(): Flow<List<FavoriteModel>> {
        return appContext.favoriteDataStore.data.map { preferences ->
            val favoriteJson = preferences[FAVORITE_LIST_KEY] ?: "[]"
            try {
                gson.fromJson<List<FavoriteModel>>(favoriteJson, favoriteListType) ?: emptyList()
            } catch (_: Exception) {
                emptyList()
            }
        }
    }
    
    override suspend fun addFavorite(video: VideoModel) {
        appContext.favoriteDataStore.edit { preferences ->
            val currentFavorites = getFavoriteListSync(preferences)
            
            // 检查是否已存在
            if (currentFavorites.any { it.videoId == video.video_id }) {
                return@edit // 已存在，不重复添加
            }
            
            val newFavorite = FavoriteModel(
                videoId = video.video_id,
                url = video.url,
                width = video.width,
                height = video.height,
                category = video.Category,
                favoriteTime = System.currentTimeMillis()
            )
            
            val updatedFavorites = currentFavorites + newFavorite
            preferences[FAVORITE_LIST_KEY] = gson.toJson(updatedFavorites)
        }
    }
    
    override suspend fun removeFavorite(videoId: String) {
        appContext.favoriteDataStore.edit { preferences ->
            val currentFavorites = getFavoriteListSync(preferences)
            val updatedFavorites = currentFavorites.filter { it.videoId != videoId }
            preferences[FAVORITE_LIST_KEY] = gson.toJson(updatedFavorites)
        }
    }
    
    override fun isFavorite(videoId: String): Flow<Boolean> {
        return getFavoriteList().map { favorites ->
            favorites.any { it.videoId == videoId }
        }
    }
    
    override suspend fun clearFavorites() {
        appContext.favoriteDataStore.edit { preferences ->
            preferences[FAVORITE_LIST_KEY] = "[]"
        }
    }
    
    /**
     * 同步获取收藏列表（用于DataStore edit块内）
     */
    private fun getFavoriteListSync(preferences: Preferences): List<FavoriteModel> {
        val favoriteJson = preferences[FAVORITE_LIST_KEY] ?: "[]"
        return try {
            gson.fromJson<List<FavoriteModel>>(favoriteJson, favoriteListType) ?: emptyList()
        } catch (_: Exception) {
            emptyList()
        }
    }
} 