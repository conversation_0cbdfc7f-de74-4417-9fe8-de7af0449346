package com.example.tikoppij.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.example.tikoppij.model.HistoryModel
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.utils.Constants
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * 历史记录管理Repository接口
 */
interface HistoryRepository {
    /**
     * 获取历史记录列表
     */
    fun getHistoryList(): Flow<List<HistoryModel>>
    
    /**
     * 添加历史记录
     */
    suspend fun addHistory(video: VideoModel, watchProgress: Long = 0)
    
    /**
     * 移除历史记录
     */
    suspend fun removeHistory(videoId: String)
    
    /**
     * 清空历史记录
     */
    suspend fun clearHistory()
    
    /**
     * 更新观看进度
     */
    suspend fun updateWatchProgress(videoId: String, progress: Long)
}

/**
 * 历史记录管理Repository实现
 */
class HistoryRepositoryImpl private constructor(
    context: Context
) : HistoryRepository {
    
    // 使用applicationContext避免内存泄漏
    private val appContext = context.applicationContext
    
    companion object {
        @Volatile
        private var INSTANCE: HistoryRepositoryImpl? = null
        
        fun getInstance(context: Context): HistoryRepositoryImpl {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: HistoryRepositoryImpl(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // DataStore扩展
        private val Context.historyDataStore: DataStore<Preferences> by preferencesDataStore(name = "history")
        
        // PreferencesKeys
        private val HISTORY_LIST_KEY = stringPreferencesKey("history_list")
    }
    
    private val gson = Gson()
    private val historyListType = object : TypeToken<List<HistoryModel>>() {}.type
    
    override fun getHistoryList(): Flow<List<HistoryModel>> {
        return appContext.historyDataStore.data.map { preferences ->
            val historyJson = preferences[HISTORY_LIST_KEY] ?: "[]"
            try {
                gson.fromJson<List<HistoryModel>>(historyJson, historyListType) ?: emptyList()
            } catch (_: Exception) { // 使用_代替未使用的参数
                emptyList()
            }
        }
    }
    
    override suspend fun addHistory(video: VideoModel, watchProgress: Long) {
        appContext.historyDataStore.edit { preferences ->
            val currentHistory = getHistoryListSync(preferences)
            
            // 移除已存在的相同视频记录（避免重复）
            val filteredHistory = currentHistory.filter { it.videoId != video.video_id }
            
            val newHistory = HistoryModel(
                videoId = video.video_id,
                url = video.url,
                width = video.width,
                height = video.height,
                category = video.Category,
                watchTime = System.currentTimeMillis(),
                watchProgress = watchProgress
            )
            
            // 将新记录添加到列表开头（最近观看的在前面）
            val updatedHistory = listOf(newHistory) + filteredHistory
            
            // 限制历史记录数量
            val limitedHistory = if (updatedHistory.size > Constants.Storage.MAX_HISTORY_COUNT) {
                updatedHistory.take(Constants.Storage.MAX_HISTORY_COUNT)
            } else {
                updatedHistory
            }
            
            preferences[HISTORY_LIST_KEY] = gson.toJson(limitedHistory)
        }
    }
    
    override suspend fun removeHistory(videoId: String) {
        appContext.historyDataStore.edit { preferences ->
            val currentHistory = getHistoryListSync(preferences)
            val updatedHistory = currentHistory.filter { it.videoId != videoId }
            preferences[HISTORY_LIST_KEY] = gson.toJson(updatedHistory)
        }
    }
    
    override suspend fun clearHistory() {
        appContext.historyDataStore.edit { preferences ->
            preferences[HISTORY_LIST_KEY] = "[]"
        }
    }
    
    override suspend fun updateWatchProgress(videoId: String, progress: Long) {
        appContext.historyDataStore.edit { preferences ->
            val currentHistory = getHistoryListSync(preferences)
            val updatedHistory = currentHistory.map { history ->
                if (history.videoId == videoId) {
                    history.copy(
                        watchProgress = progress,
                        watchTime = System.currentTimeMillis() // 更新观看时间
                    )
                } else {
                    history
                }
            }
            preferences[HISTORY_LIST_KEY] = gson.toJson(updatedHistory)
        }
    }
    
    /**
     * 同步获取历史记录列表（用于DataStore edit块内）
     */
    private fun getHistoryListSync(preferences: Preferences): List<HistoryModel> {
        val historyJson = preferences[HISTORY_LIST_KEY] ?: "[]"
        return try {
            gson.fromJson<List<HistoryModel>>(historyJson, historyListType) ?: emptyList()
        } catch (_: Exception) { // 使用_代替未使用的参数
            emptyList()
        }
    }
} 