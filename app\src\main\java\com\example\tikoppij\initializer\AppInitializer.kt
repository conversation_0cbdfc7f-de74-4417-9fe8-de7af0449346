package com.example.tikoppij.initializer

import android.content.Context
import androidx.media3.common.util.UnstableApi
import androidx.startup.Initializer
import com.example.tikoppij.network.NetworkProvider
import com.example.tikoppij.utils.PerformanceMonitor
import com.example.tikoppij.video.MediaPlayerService

/**
 * 应用统一初始化器
 * 整合性能监控、网络组件和视频播放服务的初始化
 * 作为应用唯一的基础初始化器，按正确顺序初始化所有基础组件
 */
@UnstableApi
class AppInitializer : Initializer<Unit> {
    
    companion object {
        // 单例MediaPlayerService实例
        @Volatile
        private var mediaPlayerServiceInstance: MediaPlayerService? = null
        
        /**
         * 获取媒体播放服务单例
         * 确保整个应用只有一个MediaPlayerService实例
         */
        fun getMediaPlayerService(context: Context): MediaPlayerService {
            return mediaPlayerServiceInstance ?: synchronized(this) {
                mediaPlayerServiceInstance ?: MediaPlayerService(context.applicationContext).also {
                    mediaPlayerServiceInstance = it
                }
            }
        }
    }
    
    override fun create(context: Context) {
        // 1. 初始化性能监控系统
        PerformanceMonitor.initialize()
        PerformanceMonitor.recordTimePoint("性能监控初始化完成")
        
        // 2. 初始化网络组件
        PerformanceMonitor.recordTimePoint("网络组件初始化开始")
        NetworkProvider.apiHttpClient
        PerformanceMonitor.recordTimePoint("网络组件初始化完成")
        
        // 3. 初始化媒体播放服务
        PerformanceMonitor.recordTimePoint("媒体播放服务初始化开始")
        getMediaPlayerService(context) // 确保单例被创建
        PerformanceMonitor.recordTimePoint("媒体播放服务初始化完成")
    }

    override fun dependencies(): List<Class<out Initializer<*>>> {
        // 无依赖项，这是基础组件
        return emptyList()
    }
} 