package com.example.tikoppij.model

/**
 * 视频数据模型
 * 对应接口返回的视频信息
 */
data class VideoModel(
    val url: String,         // 视频播放链接
    val video_id: String,    // 视频ID
    val Category: Int,       // 分类
    val width: Int,          // 视频宽度
    val height: Int,         // 视频高度
) {
    // 获取视频宽高比
    fun getAspectRatio(): Float {
        return if (height != 0) width.toFloat() / height.toFloat() else 16f / 9f
    }
}

/**
 * 视频列表响应模型
 * 对应接口返回的整体数据结构
 */
data class VideoResponse(
    val code: Int,
    val data: VideoListData
)

/**
 * 视频列表数据
 * 对应接口返回的data字段
 */
data class VideoListData(
    val list: List<VideoModel>
) 