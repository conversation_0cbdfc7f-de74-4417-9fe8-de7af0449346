package com.example.tikoppij.network

import com.example.tikoppij.model.VideoResponse
import com.example.tikoppij.utils.Constants
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.GET
import retrofit2.http.Query
import java.util.concurrent.TimeUnit

/**
 * 网络服务提供器
 * 整合OkHttp和Retrofit，提供应用全局共享的网络服务
 */
object NetworkProvider {
    
    /**
     * 视频API服务接口
     * 用于获取视频列表数据
     */
    interface ApiService {
        /**
         * 获取视频列表
         * @param category 分类ID
         * @param count 请求数量
         * @return 视频列表响应
         */
        @GET("v1/api/url")
        suspend fun getVideoList(
            @Query("c") category: Int = Constants.Network.DEFAULT_CATEGORY,
            @Query("r") count: Int = Constants.Network.DEFAULT_COUNT
        ): Response<VideoResponse>
    }
    
    /**
     * 获取用于视频加载的OkHttpClient
     * 针对大文件传输进行了优化
     */
    val videoHttpClient: OkHttpClient by lazy {
        createVideoHttpClient()
    }
    
    /**
     * 获取用于API请求的OkHttpClient
     */
    val apiHttpClient: OkHttpClient by lazy {
        createApiHttpClient()
    }
    
    /**
     * Retrofit API服务接口
     */
    val apiService: ApiService by lazy {
        createRetrofit().create(ApiService::class.java)
    }
    
    /**
     * 创建Retrofit实例
     */
    private fun createRetrofit(): Retrofit {
        return Retrofit.Builder()
            .baseUrl(Constants.Network.BASE_URL)
            .client(apiHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    /**
     * 创建基础Builder的私有方法，封装通用配置
     */
    private fun createBaseHttpClientBuilder(): OkHttpClient.Builder {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.NONE
        }
        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
    }
            
    /**
     * 创建视频客户端的私有方法
     */
    private fun createVideoHttpClient(): OkHttpClient {
        return createBaseHttpClientBuilder()
            .connectTimeout(Constants.Network.VIDEO_CONNECT_TIMEOUT.toLong(), TimeUnit.SECONDS)
            .readTimeout(Constants.Network.VIDEO_READ_TIMEOUT.toLong(), TimeUnit.SECONDS)
            .writeTimeout(Constants.Network.VIDEO_WRITE_TIMEOUT.toLong(), TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .followRedirects(true)
            .followSslRedirects(true)
            .build()
    }

    /**
     * 创建API客户端的私有方法
     */
    private fun createApiHttpClient(): OkHttpClient {
        return createBaseHttpClientBuilder()
            .connectTimeout(Constants.Network.API_CONNECT_TIMEOUT.toLong(), TimeUnit.SECONDS)
            .readTimeout(Constants.Network.API_READ_TIMEOUT.toLong(), TimeUnit.SECONDS)
            .writeTimeout(Constants.Network.API_WRITE_TIMEOUT.toLong(), TimeUnit.SECONDS)
            .build()
    }
} 