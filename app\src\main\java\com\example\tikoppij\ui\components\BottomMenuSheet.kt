package com.example.tikoppij.ui.components

import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.core.view.WindowCompat
import kotlin.math.roundToInt
import android.graphics.Color as AndroidGraphicsColor
import com.example.tikoppij.R // For string resources

/**
 * 自定义底部菜单组件 
 * 确保覆盖在导航栏上层，保持状态栏颜色一致
 */
@Composable
fun BottomMenuSheet(
    isVisible: Boolean,
    onDismiss: () -> Unit,
    currentDisplayMode: VideoDisplayMode,
    onToggleDisplayMode: () -> Unit,
    isAutoPlayNextEnabled: Boolean,
    onToggleAutoPlayNext: () -> Unit,
    isStatusBarVisible: Boolean = true,
    containerColor: Color = MaterialTheme.colorScheme.surfaceVariant // Use theme color
) {
    if (!isVisible) return
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false, 
            decorFitsSystemWindows = false 
        )
    ) {
        val view = LocalView.current
        DisposableEffect(view) {
            val parent = view.parent
            val window = parent?.javaClass?.getDeclaredField("window")?.apply {
                isAccessible = true
            }?.get(parent) as? android.view.Window
            
            window?.let {
                it.setBackgroundDrawableResource(android.R.color.transparent)
                it.setDimAmount(0f) 
                WindowCompat.setDecorFitsSystemWindows(it, false)
                @Suppress("DEPRECATION")
                it.apply {
                    statusBarColor = AndroidGraphicsColor.TRANSPARENT
                    navigationBarColor = AndroidGraphicsColor.TRANSPARENT
                }
                WindowCompat.getInsetsController(it, view).apply {
                    isAppearanceLightStatusBars = false 
                    isAppearanceLightNavigationBars = false 
                    
                    if (isStatusBarVisible) {
                        show(androidx.core.view.WindowInsetsCompat.Type.statusBars())
                    } else {
                        hide(androidx.core.view.WindowInsetsCompat.Type.statusBars())
                    }
                }
            }
            onDispose {}
        }
        
        var sheetHeight by remember { mutableFloatStateOf(0f) }
        var dragOffset by remember { mutableFloatStateOf(0f) }
        var isDragging by remember { mutableStateOf(false) }
        var shouldDismiss by remember { mutableStateOf(false) }
        
        val animationSpec: AnimationSpec<Float> = spring(
            dampingRatio = 0.8f, 
            stiffness = 400f 
        )
        
        val animatedOffset by animateFloatAsState(
            targetValue = if (isDragging) {
                dragOffset / sheetHeight.coerceAtLeast(1f) // Avoid division by zero if sheetHeight is 0
            } else if (shouldDismiss) {
                1.0f
            } else {
                0f
            },
            animationSpec = animationSpec,
            label = "sheetOffset",
            finishedListener = { 
                if (shouldDismiss) {
                    onDismiss()
                }
            }
        )
        
        val backgroundAlpha = remember(animatedOffset) {
            (1 - animatedOffset * 1.5f).coerceIn(0f, 0.5f)
        }
        
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black.copy(alpha = backgroundAlpha))
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null
                ) { 
                    shouldDismiss = true
                }, 
            contentAlignment = Alignment.BottomCenter
        ) {
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .offset { 
                        IntOffset(
                            x = 0, 
                            y = (animatedOffset * sheetHeight).roundToInt()
                        ) 
                    }
                    .onGloballyPositioned {
                        sheetHeight = it.size.height.toFloat()
                    }
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null
                    ) { /* 拦截点击，防止传递到背景 */ }
                    .pointerInput(Unit) {
                        detectVerticalDragGestures(
                            onDragStart = { isDragging = true },
                            onDragEnd = {
                                isDragging = false
                                if (dragOffset > sheetHeight * 0.12f) {
                                    shouldDismiss = true
                                } else {
                                    dragOffset = 0f
                                }
                            },
                            onDragCancel = {
                                dragOffset = 0f
                                isDragging = false
                            },
                            onVerticalDrag = { change, dragAmount ->
                                val dampedDragAmount = if (dragOffset > sheetHeight * 0.5f) {
                                    dragAmount * 0.5f 
                                } else {
                                    dragAmount * 0.8f 
                                }
                                dragOffset = (dragOffset + dampedDragAmount).coerceAtLeast(0f)
                                change.consume()
                            }
                        )
                    },
                shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
                color = containerColor,
                shadowElevation = 8.dp
            ) {
                Column(modifier = Modifier.padding(bottom = 16.dp)) { // Add padding to the bottom of the column
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 12.dp)
                            .height(24.dp) 
                            .align(Alignment.CenterHorizontally),
                        contentAlignment = Alignment.Center
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth(0.15f) 
                                .height(4.dp)
                                .clip(RoundedCornerShape(2.dp))
                                .background(Color.Gray.copy(alpha = 0.6f)) 
                        )
                    }
                    
                    // Settings Items
                    SettingItemSwitch(
                        title = stringResource(id = R.string.display_mode_setting),
                        description = stringResource(id = if (currentDisplayMode == VideoDisplayMode.AUTO_ADAPT) R.string.display_mode_auto_adapt else R.string.display_mode_fit),
                        checked = currentDisplayMode == VideoDisplayMode.FIT, // FIT mode means switch is ON (for toggling to AUTO_ADAPT)
                        onCheckedChanged = { onToggleDisplayMode() }
                    )

                    SettingItemSwitch(
                        title = stringResource(id = R.string.auto_play_next_setting),
                        description = stringResource(id = if (isAutoPlayNextEnabled) R.string.auto_play_next_enabled else R.string.auto_play_next_disabled),
                        checked = isAutoPlayNextEnabled,
                        onCheckedChanged = { onToggleAutoPlayNext() }
                    )
                    
                    // Bottom safe area (already present, good for spacing)
                    Spacer(modifier = Modifier.height(44.dp)) // Adjusted height for better spacing
                }
            }
        }
    }
}

@Composable
private fun SettingItemSwitch(
    title: String,
    description: String,
    checked: Boolean,
    onCheckedChanged: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onCheckedChanged(!checked) } // Allow clicking the whole row
            .padding(horizontal = 24.dp, vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(text = title, style = MaterialTheme.typography.titleMedium, fontSize = 16.sp)
            Text(text = description, style = MaterialTheme.typography.bodySmall, color = MaterialTheme.colorScheme.onSurfaceVariant)
        }
        Spacer(modifier = Modifier.width(16.dp))
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChanged
        )
    }
} 