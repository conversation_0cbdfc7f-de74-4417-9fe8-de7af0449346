package com.example.tikoppij.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import com.example.tikoppij.ui.navigation.BottomNavItem
import com.example.tikoppij.ui.theme.VideoPlayerBackground

/**
 * 自定义底部导航栏组件
 */
@Composable
fun BottomNavBar(
    navController: NavController,
    items: List<BottomNavItem>,
    containerColor: Color,
    contentColor: Color // 这个是选中项的颜色
) {
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route

    // 选择灰色作为未选中项的颜色，根据背景色调整
    val unselectedColor = if (containerColor == VideoPlayerBackground) Color.LightGray else Color.Gray

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(50.dp) // 将高度从56.dp降低到50.dp
            .background(containerColor)
            .padding(horizontal = 8.dp),
        horizontalArrangement = Arrangement.SpaceAround,
        verticalAlignment = Alignment.CenterVertically
    ) {
        items.forEach { item ->
            val isSelected = currentRoute == item.route
            val currentItemColor = if (isSelected) contentColor else unselectedColor
            val interactionSource = remember { MutableInteractionSource() } // 为clickable创建一个interactionSource

            // 根据是否选中来调整文字样式
            val currentTextStyle = MaterialTheme.typography.bodyLarge
            val textStyle = if (isSelected) {
                currentTextStyle.copy(fontSize = currentTextStyle.fontSize * 1.1f) // 放大10%
            } else {
                currentTextStyle
            }

            Box(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .clickable(
                        interactionSource = interactionSource,
                        indication = null, // 移除点击时的涟漪效果
                        onClickLabel = item.title, // 为无障碍功能提供标签
                        onClick = {
                            if (currentRoute != item.route) {
                                navController.navigate(item.route) {
                                    popUpTo(navController.graph.startDestinationId) {
                                        saveState = true
                                    }
                                    launchSingleTop = true
                                    restoreState = true
                                }
                            }
                        }
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = item.title,
                    style = textStyle, // 应用调整后的样式
                    color = currentItemColor
                )
            }
        }
    }
} 