package com.example.tikoppij.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.DismissibleDrawerSheet
import androidx.compose.material3.DismissibleNavigationDrawer
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import com.example.tikoppij.ui.theme.AppDarkGrey
import com.example.tikoppij.ui.theme.NavBarContentColor

/**
 * 自定义左侧抽屉组件
 * 
 * @param isOpen 抽屉是否打开
 * @param onDismiss 关闭抽屉时的回调
 * @param drawerContent 抽屉内容
 * @param content 主内容
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NavigationDrawerComponent(
    isOpen: Boolean,
    onDismiss: () -> Unit,
    drawerContent: @Composable () -> Unit,
    content: @Composable () -> Unit
) {
    // 创建并记住抽屉状态
    val drawerState = rememberDrawerState(
        initialValue = if (isOpen) DrawerValue.Open else DrawerValue.Closed
    )
    
    // 当外部isOpen状态改变时，同步更新drawerState
    LaunchedEffect(isOpen) {
        if (isOpen) {
            drawerState.open()
        } else {
            drawerState.close()
        }
    }
    
    // 使用DismissibleNavigationDrawer组件
    DismissibleNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            DismissibleDrawerSheet(
                drawerContainerColor = AppDarkGrey, // 使用统一的颜色常量
                modifier = Modifier
                    .width(340.dp) // 增加抽屉宽度，使主页面显示更少
                    .fillMaxHeight()
                    // 移除阴影效果，以消除交接处的视觉差异
            ) {
                drawerContent()
            }
        },
        content = {
            // 为内容创建一个容器
            Box(modifier = Modifier.fillMaxSize()) {
                // 当抽屉打开时，整个背景使用与抽屉相同的颜色
                if (drawerState.currentValue == DrawerValue.Open) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(AppDarkGrey)
                    )
                }
                
                // 内容区域应用圆角效果
                val contentModifier = if (drawerState.currentValue == DrawerValue.Open) {
                    Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(topStart = 20.dp, bottomStart = 20.dp))
                } else {
                    Modifier.fillMaxSize()
                }
                
                // 使用修饰符包装内容
                Box(modifier = contentModifier) {
                    content()
                }
            }
        },
        gesturesEnabled = true // 启用手势滑动
    )
}

/**
 * 预设的抽屉内容组件
 * 可以根据需要自定义
 */
@Composable
fun DefaultDrawerContent() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 顶部间距
        Spacer(modifier = Modifier.height(24.dp))
        
        // 这里可以添加抽屉内容，如菜单项等
        // 示例：
        Text(
            text = "抽屉菜单项1",
            color = NavBarContentColor,
            modifier = Modifier.padding(vertical = 12.dp)
        )
        
        Text(
            text = "抽屉菜单项2",
            color = NavBarContentColor,
            modifier = Modifier.padding(vertical = 12.dp)
        )
        
        Text(
            text = "抽屉菜单项3",
            color = NavBarContentColor,
            modifier = Modifier.padding(vertical = 12.dp)
        )
    }
} 