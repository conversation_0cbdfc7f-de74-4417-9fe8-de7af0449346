package com.example.tikoppij.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.tikoppij.R
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.utils.InternalDownloadManager
import com.example.tikoppij.utils.ShareUtils
import kotlinx.coroutines.launch

/**
 * 通用视频控制按钮组件
 * 用于主页、收藏页面、播放页面的右下角功能按钮
 */
@Composable
fun VideoControlButtons(
    modifier: Modifier = Modifier,
    currentVideo: VideoModel? = null,
    isFavorite: Boolean = false,
    onFavoriteClick: () -> Unit = {},
    onDownloadClick: () -> Unit = {},
    onShareClick: () -> Unit = {},
    onMenuClick: (() -> Unit)? = null,
    onToggleBottomBarVisibility: (() -> Unit)? = null,
    isBottomBarVisible: Boolean = true,
    showToggleButton: Boolean = true,
    showMenuButton: Boolean = true
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    
    var showDownloadDialog by remember { mutableStateOf(false) }
    var isDownloading by remember { mutableStateOf(false) }
    var downloadProgress by remember { mutableFloatStateOf(0f) }
    
    Column(
        modifier = modifier
    ) {
        // 收藏按钮
        VideoControlButton(
            onClick = onFavoriteClick
        ) {
            Icon(
                painter = painterResource(id = if (isFavorite) R.drawable.ic_favorite else R.drawable.ic_favorite_border),
                contentDescription = null,
                tint = if (isFavorite) Color.Red else Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 下载按钮
        VideoControlButton(
            onClick = { 
                if (!isDownloading) {
                    showDownloadDialog = true
                }
                onDownloadClick()
            }
        ) {
            if (isDownloading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = Color.White,
                    strokeWidth = 2.dp
                )
            } else {
                Icon(
                    painter = painterResource(id = R.drawable.ic_download),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 分享按钮
        VideoControlButton(
            onClick = { 
                currentVideo?.let { video ->
                    ShareUtils.shareVideoInfo(context, video.video_id, video.url)
                }
                onShareClick()
            }
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_share),
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
        
        // 隐藏/显示底部导航栏按钮（仅在主页显示）
        if (showToggleButton && onToggleBottomBarVisibility != null) {
            Spacer(modifier = Modifier.height(12.dp))
            
            VideoControlButton(
                onClick = onToggleBottomBarVisibility
            ) {
                Icon(
                    painter = painterResource(id = if (isBottomBarVisible) R.drawable.ic_visibility_off else R.drawable.ic_visibility),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
        
        // 菜单按钮
        if (showMenuButton && onMenuClick != null) {
            Spacer(modifier = Modifier.height(12.dp))
            
            VideoControlButton(
                onClick = onMenuClick
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_menu),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
    
    // 下载确认对话框
    if (showDownloadDialog) {
        AlertDialog(
            onDismissRequest = { 
                showDownloadDialog = false 
            },
            title = { 
                Text(text = "下载视频") 
            },
            text = { 
                Text("确定要下载这个视频吗？\n\n视频将保存到 Downloads/TikoppiVideos 文件夹，您可以在文件管理器中找到。\n\n系统会显示下载进度通知。")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        currentVideo?.let { video ->
                            showDownloadDialog = false
                            
                            coroutineScope.launch {
                                InternalDownloadManager.downloadVideo(
                                    context = context,
                                    videoUrl = video.url,
                                    onComplete = { success, result ->
                                        // 下载已开始或完成的处理
                                        // 系统会显示通知，这里不需要额外处理
                                    }
                                )
                            }
                        }
                    },
                    colors = ButtonDefaults.textButtonColors(
                        containerColor = Color.Black,
                        contentColor = Color.White
                    ),
                    shape = RoundedCornerShape(4.dp)
                ) {
                    Text("确认")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDownloadDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        containerColor = Color.Black,
                        contentColor = Color.White
                    ),
                    shape = RoundedCornerShape(4.dp)
                ) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 单个视频控制按钮
 * 优化为黑色背景
 */
@Composable
private fun VideoControlButton(
    onClick: () -> Unit,
    content: @Composable () -> Unit
) {
    Box(
        modifier = Modifier
            .size(48.dp)
            .clip(RoundedCornerShape(24.dp))
            .background(Color.Black.copy(alpha = 0.6f))
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        content()
    }
} 