package com.example.tikoppij.ui.components

import android.view.TextureView
import android.view.ViewGroup.LayoutParams.MATCH_PARENT
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.R
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.video.MediaPlayerService
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 视频显示模式枚举
 */
enum class VideoDisplayMode {
    AUTO_ADAPT, // 自动适应模式：横屏完整显示，竖屏填充
    FIT        // 适应模式：所有视频都完整显示，可能有黑边
}

/**
 * TextureView vs SurfaceView选择说明:
 * 选择TextureView原因: 支持UI交互和动画效果，与Compose UI集成更好，适合短视频滚动场景
 */

/**
 * 统一视频播放器组件
 */
@UnstableApi
@Composable
fun VideoPlayerComponent(
    video: VideoModel,
    playerManager: MediaPlayerService,
    pageIndex: Int = -1,
    isCurrentItem: Boolean = false,
    isVisible: Boolean = false,
    displayMode: VideoDisplayMode = VideoDisplayMode.AUTO_ADAPT,
    onIsPlayingChanged: (Boolean) -> Unit,
    onLongPress: (() -> Unit)? = null
) {
    val haptic = LocalHapticFeedback.current
    var isPlayerReady by remember { mutableStateOf(false) }
    var isPlaying by remember { mutableStateOf(playerManager.getPlaybackState(pageIndex) != false) }
    var showControls by remember { mutableStateOf(false) }
    var progress by remember { mutableFloatStateOf(0f) }
    var duration by remember { mutableLongStateOf(0L) }
    var isDragging by remember { mutableStateOf(false) }
    
    val iconSize by animateDpAsState(
        targetValue = if (!isPlaying) 56.dp else 48.dp,
        animationSpec = tween(300),
        label = "iconSize"
    )
    
    val aspectRatio = video.getAspectRatio()
    
    val controlIcon: Painter = if (isPlaying) {
        painterResource(id = R.drawable.ic_pause)
    } else {
        painterResource(id = R.drawable.ic_play)
    }
    
    fun handlePlayPauseAction() {
        if (isPlayerReady) {
            isPlaying = !isPlaying
            onIsPlayingChanged(isPlaying)
            
            if (isPlaying) {
                playerManager.play(pageIndex)
                MainScope().launch {
                    showControls = true
                    delay(1500)
                    showControls = false
                }
            } else {
                playerManager.pause(pageIndex)
                showControls = true
            }
            
            playerManager.savePlaybackState(pageIndex, isPlaying)
        }
    }
    
    LaunchedEffect(isCurrentItem, isVisible, isPlaying, isPlayerReady) {
        if (isCurrentItem && isVisible && isPlayerReady) {
            val currentPlayer = playerManager.getPlayerByIndex(pageIndex)
            currentPlayer?.let {
                if (duration <= 0 && it.duration > 0) {
                    duration = it.duration
                }
            }
            
            while (true) {
                if (isPlaying && !isDragging) {
                    val progressPlayer = playerManager.getPlayerByIndex(pageIndex)
                    progressPlayer?.let {
                        if (duration > 0) {
                            progress = (it.currentPosition.toFloat() / duration).coerceIn(0f, 1f)
                        }
                    }
                }
                delay(100)
            }
        }
    }
    
    LaunchedEffect(showControls) {
        if (showControls) {
            delay(2000)
            showControls = false
        }
    }
    
    LaunchedEffect(isCurrentItem, isVisible) {
        if (isCurrentItem && isVisible && isPlayerReady) {
            val player = playerManager.getPlayerByIndex(pageIndex)
            
            isPlaying = true
            onIsPlayingChanged(true)
            playerManager.savePlaybackState(pageIndex, true)
            
            isDragging = false
            delay(300)
            showControls = false

            player?.let { exoPlayer ->
                if (exoPlayer.playbackState == Player.STATE_ENDED) {
                    exoPlayer.seekTo(0)
                }
                exoPlayer.play()
            }
            
            playerManager.play(pageIndex)
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { handlePlayPauseAction() },
                    onLongPress = {
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        onLongPress?.invoke()
                    }
                )
            }
    ) {
        AndroidView(
            modifier = Modifier
                .fillMaxSize()
                .run {
                    when (displayMode) {
                        VideoDisplayMode.AUTO_ADAPT -> {
                            val thresholdRatioForFillingHeight = 0.7f 

                            if (aspectRatio > thresholdRatioForFillingHeight) {
                                aspectRatio(aspectRatio, matchHeightConstraintsFirst = false)
                            } else {
                                aspectRatio(aspectRatio, matchHeightConstraintsFirst = true)
                            }
                        }
                        VideoDisplayMode.FIT -> {
                            aspectRatio(aspectRatio, matchHeightConstraintsFirst = false)
                        }
                    }
                }
            ,
            factory = { ctx ->
                TextureView(ctx).apply {
                    layoutParams = android.widget.FrameLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT)
                    val surfacePlayer = playerManager.getOrCreatePlayer(video, pageIndex)
                    surfacePlayer.setVideoTextureView(this)
                    
                    if (!isPlaying) {
                        surfacePlayer.pause()
                    }
                    
                    surfacePlayer.addListener(object : Player.Listener {
                        override fun onPlaybackStateChanged(state: Int) {
                            if (state == Player.STATE_READY) {
                                duration = surfacePlayer.duration
                            }
                        }
                    })
                    
                    isPlayerReady = true
                }
            },
            update = { textureView ->
                val updatePlayer = playerManager.getOrCreatePlayer(video, pageIndex)
                updatePlayer.setVideoTextureView(textureView)
            }
        )
        
        if (showControls && isVisible) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = controlIcon,
                    contentDescription = if (isPlaying) stringResource(id = R.string.pause_video) else stringResource(id = R.string.play_video),
                    tint = Color.White,
                    modifier = Modifier.size(iconSize)
                )
            }
        }
        
        if ((!isPlaying || isDragging) && isVisible) {
            val trackHeight = 4.dp
            val thumbSize = 12.dp
            val progressBarTouchableHeight = thumbSize + 8.dp 

            BoxWithConstraints(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 16.dp)
                    .fillMaxWidth(0.95f) 
                    .height(progressBarTouchableHeight)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            if (isPlayerReady) {
                                val player = playerManager.getPlayerByIndex(pageIndex)
                                player?.let { exoPlayer ->
                                    val totalDuration = exoPlayer.duration
                                    if (totalDuration <= 0) return@let

                                    val newProgress = (it.x / size.width).coerceIn(0f, 1f)
                                    progress = newProgress
                                    exoPlayer.seekTo((totalDuration * newProgress).toLong())

                                    if (!isPlaying) {
                                        isPlaying = true
                                        onIsPlayingChanged(true)
                                        exoPlayer.play()
                                    }
                                    playerManager.savePlaybackState(pageIndex, true)
                                }
                            }
                        }
                    }
                    .pointerInput(Unit) {
                        detectDragGestures(
                            onDragStart = { 
                                isDragging = true 
                                val player = playerManager.getPlayerByIndex(pageIndex)
                                player?.let {
                                    if (isPlaying) {
                                        it.pause() 
                                    }
                                }
                            },
                            onDragEnd = {
                                isDragging = false
                                val player = playerManager.getPlayerByIndex(pageIndex)
                                player?.let {
                                    isPlaying = true
                                    onIsPlayingChanged(true)
                                    it.play()
                                    playerManager.savePlaybackState(pageIndex, true)
                                }
                            },
                            onDragCancel = {
                                isDragging = false
                                val player = playerManager.getPlayerByIndex(pageIndex)
                                player?.let {
                                    isPlaying = true
                                    onIsPlayingChanged(true)
                                    it.play()
                                    playerManager.savePlaybackState(pageIndex, true)
                                }
                            },
                            onDrag = { change, _ -> 
                                if (isPlayerReady) {
                                    val player = playerManager.getPlayerByIndex(pageIndex)
                                    player?.let { exoPlayer ->
                                        val totalDuration = exoPlayer.duration
                                        if (totalDuration <= 0) return@let
                                        val newProgress = (change.position.x / size.width).coerceIn(0f, 1f)
                                        progress = newProgress
                                        exoPlayer.seekTo((totalDuration * newProgress).toLong())
                                    }
                                }
                            }
                        )
                    }
            ) {
                val maxWidthInDp = this.maxWidth
                val bwcWidthPx = LocalDensity.current.run { maxWidthInDp.toPx() }
                val thumbSizePx = LocalDensity.current.run { thumbSize.toPx() }

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(trackHeight)
                        .background(Color.DarkGray.copy(alpha = 0.6f), RoundedCornerShape(trackHeight / 2))
                        .align(Alignment.Center)
                )
                Box(
                    modifier = Modifier
                        .fillMaxWidth(progress)
                        .height(trackHeight)
                        .background(Color.White, RoundedCornerShape(trackHeight / 2))
                        .align(Alignment.CenterStart)
                )
                Box(
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .size(thumbSize)
                        .graphicsLayer {
                            translationX = (bwcWidthPx * progress) - (thumbSizePx / 2)
                        }
                        .background(Color.White, CircleShape)
                ) 
            }
        }
    }
    
    DisposableEffect(isCurrentItem, isVisible) {
        if (isCurrentItem && isVisible && isPlayerReady) {
            playerManager.play(pageIndex)
        } else if (!isVisible && isPlayerReady) {
            playerManager.pause(pageIndex, updateActiveStatus = false)
        }
        
        onDispose {
            if (isPlayerReady) {
                playerManager.pause(pageIndex, updateActiveStatus = false)
            }
        }
    }
} 