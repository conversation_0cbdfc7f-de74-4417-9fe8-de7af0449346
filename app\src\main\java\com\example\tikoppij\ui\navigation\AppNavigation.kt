package com.example.tikoppij.ui.navigation

import androidx.compose.animation.EnterTransition
import androidx.compose.animation.ExitTransition
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.media3.common.util.UnstableApi
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.example.tikoppij.R
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.ui.screens.CacheManagementScreen
import com.example.tikoppij.ui.screens.FavoriteListScreen
import com.example.tikoppij.ui.screens.HiddenScreen
import com.example.tikoppij.ui.screens.HistoryListScreen
import com.example.tikoppij.ui.screens.ProfileScreen
import com.example.tikoppij.ui.screens.ToolsScreen
import com.example.tikoppij.ui.screens.VideoScreen
import com.example.tikoppij.ui.screens.VideoPlayerScreen

/**
 * 应用导航路由
 */
sealed class AppDestinations(val route: String) {
    data object Home : AppDestinations("home")
    data object Tools : AppDestinations("tools")
    data object Hidden : AppDestinations("hidden")
    data object Profile : AppDestinations("profile")
    data object CacheManagement : AppDestinations("cache_management")
    data object FavoriteList : AppDestinations("favorite_list")
    data object HistoryList : AppDestinations("history_list")
    
    // 独立播放页面路由
    data object FavoritePlayer : AppDestinations("favorite_player")
    data object HistoryPlayer : AppDestinations("history_player")
}

/**
 * 底部导航项目数据类
 */
data class BottomNavItem(
    val route: String,
    val title: String,
    val iconResId: Int
)

/**
 * 底部导航栏项目列表
 */
val bottomNavItems = listOf(
    BottomNavItem(
        route = AppDestinations.Home.route,
        title = "首页",
        iconResId = R.drawable.ic_home
    ),
    BottomNavItem(
        route = AppDestinations.Tools.route,
        title = "工具",
        iconResId = R.drawable.ic_tools
    ),
    BottomNavItem(
        route = AppDestinations.Hidden.route,
        title = "隐藏",
        iconResId = R.drawable.ic_hidden
    ),
    BottomNavItem(
        route = AppDestinations.Profile.route,
        title = "我的",
        iconResId = R.drawable.ic_profile
    )
)

/**
 * 通用屏幕包裹组件
 */
@Composable
private fun ScreenWrapper(content: @Composable () -> Unit) {
    Box {
        content()
    }
}

@UnstableApi
@Composable
fun AppNavGraph(
    navController: NavHostController,
    innerPadding: PaddingValues,
    isBottomBarVisible: Boolean,
    isStatusBarVisible: Boolean,
    toggleBottomBarVisibility: () -> Unit
) {
    // 用于存储播放数据的状态
    var pendingVideoList by remember { mutableStateOf<List<VideoModel>?>(null) }
    var pendingStartIndex by remember { mutableIntStateOf(0) }

    NavHost(
        navController = navController,
        startDestination = AppDestinations.Home.route,
        modifier = Modifier
            .fillMaxSize()
            .padding(
                start = innerPadding.calculateLeftPadding(androidx.compose.ui.unit.LayoutDirection.Ltr),
                end = innerPadding.calculateRightPadding(androidx.compose.ui.unit.LayoutDirection.Ltr),
                bottom = innerPadding.calculateBottomPadding()
            ),
        enterTransition = { EnterTransition.None },
        exitTransition = { ExitTransition.None },
        popEnterTransition = { EnterTransition.None },
        popExitTransition = { ExitTransition.None }
    ) {
        // 首页
        composable(route = AppDestinations.Home.route) {
            VideoScreen(
                isBottomBarVisible = isBottomBarVisible,
                toggleBottomBarVisibility = toggleBottomBarVisibility,
                isStatusBarVisible = isStatusBarVisible
            )
        }
        
        // 其他页面
        composable(route = AppDestinations.Tools.route) {
            ScreenWrapper {
                ToolsScreen()
            }
        }
        
        composable(route = AppDestinations.Hidden.route) {
            ScreenWrapper {
                HiddenScreen()
            }
        }
        
        composable(route = AppDestinations.Profile.route) {
            ScreenWrapper {
                ProfileScreen(
                    onNavigateToCacheManagement = {
                        navController.navigate(AppDestinations.CacheManagement.route)
                    },
                    onNavigateToFavoriteList = {
                        navController.navigate(AppDestinations.FavoriteList.route)
                    },
                    onNavigateToHistoryList = {
                        navController.navigate(AppDestinations.HistoryList.route)
                    }
                )
            }
        }
        
        // 缓存管理页面
        composable(route = AppDestinations.CacheManagement.route) {
            ScreenWrapper {
                CacheManagementScreen(
                    navController = navController
                )
            }
        }
        
        // 收藏列表页面
        composable(route = AppDestinations.FavoriteList.route) {
            FavoriteListScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToPlayer = { videoList, startIndex ->
                    pendingVideoList = videoList
                    pendingStartIndex = startIndex
                    navController.navigate(AppDestinations.FavoritePlayer.route)
                }
            )
        }
        
        // 历史记录列表页面
        composable(route = AppDestinations.HistoryList.route) {
            HistoryListScreen(
                onNavigateBack = {
                    navController.popBackStack()
                },
                onNavigateToPlayer = { videoList, startIndex ->
                    pendingVideoList = videoList
                    pendingStartIndex = startIndex
                    navController.navigate(AppDestinations.HistoryPlayer.route)
                }
            )
        }
        
        // 收藏播放页面
        composable(route = AppDestinations.FavoritePlayer.route) {
            pendingVideoList?.let { videoList ->
                VideoPlayerScreen(
                    videoList = videoList,
                    startIndex = pendingStartIndex
                )
            }
        }
        
        // 历史播放页面
        composable(route = AppDestinations.HistoryPlayer.route) {
            pendingVideoList?.let { videoList ->
                VideoPlayerScreen(
                    videoList = videoList,
                    startIndex = pendingStartIndex
                )
            }
        }
    }
} 