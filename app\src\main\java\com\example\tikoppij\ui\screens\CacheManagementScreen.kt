package com.example.tikoppij.ui.screens

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.media3.common.util.UnstableApi
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.example.tikoppij.R
import com.example.tikoppij.viewmodel.CacheManagementViewModel

/**
 * 缓存管理屏幕
 * 用于显示和管理视频缓存设置
 */
@OptIn(ExperimentalMaterial3Api::class)
@UnstableApi
@Composable
fun CacheManagementScreen(
    viewModel: CacheManagementViewModel = viewModel(),
    navController: NavController = rememberNavController()
) {
    val currentCacheSizeFormatted by viewModel.currentCacheSizeFormatted.collectAsState()
    val maxCacheSizeFormatted by viewModel.maxCacheSizeFormatted.collectAsState()
    val cacheUsagePercent by viewModel.cacheUsagePercent.collectAsState()
    val isClearingCache by viewModel.isClearingCache.collectAsState()
    val maxCacheSize by viewModel.maxCacheSize.collectAsState()
    
    // 动画进度条值
    val animatedProgress by animateFloatAsState(
        targetValue = cacheUsagePercent,
        label = "cacheUsageProgress"
    )
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("缓存管理") },
                navigationIcon = {
                    IconButton(onClick = { navController.popBackStack() }) {
                        Icon(
                            painter = painterResource(id = R.drawable.ic_arrow_back),
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 缓存使用情况卡片
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "缓存使用情况",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 进度条
                    LinearProgressIndicator(
                        progress = { animatedProgress },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(8.dp)
                            .clip(RoundedCornerShape(4.dp))
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 使用情况文字
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "已使用: $currentCacheSizeFormatted",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Text(
                            text = "总容量: $maxCacheSizeFormatted",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
            
            // 缓存上限设置卡片
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "缓存上限设置",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 缓存大小选项
                    viewModel.cacheSizeOptions.forEach { sizeMB ->
                        val sizeText = if (sizeMB >= 1024) {
                            "${sizeMB / 1024}GB"
                        } else {
                            "${sizeMB}MB"
                        }
                        
                        val sizeBytes = sizeMB * 1024L * 1024L
                        val isSelected = maxCacheSize == sizeBytes
                        
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { viewModel.setMaxCacheSize(sizeMB) }
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = isSelected,
                                onClick = { viewModel.setMaxCacheSize(sizeMB) }
                            )
                            Text(
                                text = sizeText,
                                style = MaterialTheme.typography.bodyLarge,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }
            }
            
            // 缓存清理卡片
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "缓存清理",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "清除所有缓存将删除已缓存的视频，需要重新下载。",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.Gray
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Button(
                        onClick = { 
                            // 只调用clearAllCache()，不再调用refreshCacheInfo()
                            // 因为refreshCacheInfo()可能会覆盖我们设置的0值
                            viewModel.clearAllCache() 
                        },
                        modifier = Modifier.fillMaxWidth(),
                        enabled = !isClearingCache
                    ) {
                        if (isClearingCache) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                color = MaterialTheme.colorScheme.onPrimary,
                                strokeWidth = 2.dp
                            )
                        } else {
                            Text("清除所有缓存")
                        }
                    }
                }
            }
        }
    }
} 