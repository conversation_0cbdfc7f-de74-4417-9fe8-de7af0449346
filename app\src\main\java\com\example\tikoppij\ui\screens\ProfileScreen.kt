package com.example.tikoppij.ui.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.tikoppij.R

/**
 * 个人资料屏幕
 */
@Composable
fun ProfileScreen(
    onNavigateToCacheManagement: () -> Unit = {},
    onNavigateToFavoriteList: () -> Unit = {},
    onNavigateToHistoryList: () -> Unit = {}
) {
    val scrollState = rememberScrollState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .verticalScroll(scrollState)
    ) {
        // 用户信息卡片
        UserInfoCard()
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 我的内容菜单
        MyContentMenuCard(
            onNavigateToFavoriteList = onNavigateToFavoriteList,
            onNavigateToHistoryList = onNavigateToHistoryList
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 设置菜单
        SettingsMenuCard(onNavigateToCacheManagement)
    }
}

/**
 * 用户信息卡片
 */
@Composable
private fun UserInfoCard() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 头像
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .clip(CircleShape)
                    .background(MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_profile),
                    contentDescription = "Profile Picture",
                    modifier = Modifier.size(40.dp),
                    contentScale = ContentScale.Fit
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 用户名
            Text(
                text = "用户名",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 用户ID
            Text(
                text = "ID: 12345678",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 用户统计信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem("关注", "0")
                StatItem("粉丝", "0")
                StatItem("获赞", "0")
            }
        }
    }
}

/**
 * 统计项目
 */
@Composable
private fun StatItem(label: String, value: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray
        )
    }
}

/**
 * 我的内容菜单卡片
 */
@Composable
private fun MyContentMenuCard(
    onNavigateToFavoriteList: () -> Unit,
    onNavigateToHistoryList: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "我的内容",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 收藏列表菜单项
            SettingMenuItem(
                useDrawableIcon = true,
                drawableIconRes = R.drawable.ic_favorite,
                title = "我的收藏",
                subtitle = "查看收藏的视频",
                onClick = onNavigateToFavoriteList
            )
            
            HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))
            
            // 历史记录菜单项
            SettingMenuItem(
                useDrawableIcon = true,
                drawableIconRes = R.drawable.ic_history,
                title = "观看历史",
                subtitle = "查看观看过的视频",
                onClick = onNavigateToHistoryList
            )
        }
    }
}

/**
 * 设置菜单卡片
 */
@Composable
private fun SettingsMenuCard(onNavigateToCacheManagement: () -> Unit) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "设置",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // 缓存管理菜单项
            SettingMenuItem(
                useDrawableIcon = true,
                drawableIconRes = R.drawable.ic_settings,
                title = "缓存管理",
                subtitle = "管理视频缓存、设置缓存上限",
                onClick = onNavigateToCacheManagement
            )
            
            HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))
            
            // 其他设置项
            SettingMenuItem(
                useDrawableIcon = true,
                drawableIconRes = R.drawable.ic_settings,
                title = "通知设置",
                subtitle = "管理应用通知"
            )
            
            HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))
            
            SettingMenuItem(
                useDrawableIcon = true,
                drawableIconRes = R.drawable.ic_settings,
                title = "隐私设置",
                subtitle = "管理隐私选项"
            )
            
            HorizontalDivider(modifier = Modifier.padding(vertical = 8.dp))
            
            SettingMenuItem(
                useDrawableIcon = true,
                drawableIconRes = R.drawable.ic_settings,
                title = "关于应用",
                subtitle = "版本信息、使用条款"
            )
        }
    }
}

/**
 * 设置菜单项
 */
@Composable
private fun SettingMenuItem(
    icon: ImageVector? = null,
    title: String,
    subtitle: String,
    onClick: () -> Unit = {},
    useDrawableIcon: Boolean = false,
    drawableIconRes: Int? = null
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 图标
        if (useDrawableIcon && drawableIconRes != null) {
            Icon(
                painter = painterResource(id = drawableIconRes),
                contentDescription = title,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
        } else if (icon != null) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        // 文本内容
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodySmall,
                color = Color.Gray
            )
        }
        
        // 箭头图标
        Icon(
            painter = painterResource(id = R.drawable.ic_arrow_forward),
            contentDescription = "Navigate",
            tint = Color.Gray,
            modifier = Modifier.size(20.dp)
        )
    }
} 