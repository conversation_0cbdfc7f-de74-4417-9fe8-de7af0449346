package com.example.tikoppij.ui.screens

import android.app.Activity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.VerticalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.ui.components.BottomMenuSheet
import com.example.tikoppij.ui.components.VideoControlButtons
import com.example.tikoppij.ui.components.VideoPlayerComponent
import com.example.tikoppij.utils.LifecycleManager
import com.example.tikoppij.viewmodel.VideoViewModel

/**
 * 视频屏幕
 * 使用VerticalPager实现垂直滑动的视频页面
 */
@UnstableApi
@Composable
fun VideoScreen(
    modifier: Modifier = Modifier,
    viewModel: VideoViewModel = viewModel(),
    isBottomBarVisible: Boolean,
    toggleBottomBarVisibility: () -> Unit,
    isStatusBarVisible: Boolean
) {
    val videoList by viewModel.videoList.collectAsState()
    val currentIndex by viewModel.currentIndex.collectAsState()
    val displayMode by viewModel.videoDisplayMode.collectAsState()
    val autoPlayNextEnabled by viewModel.autoPlayNextEnabled.collectAsState()
    val currentVideoIsFavorite by viewModel.currentVideoIsFavorite.collectAsState()

    // 用于追踪当前显示页面的播放状态
    var currentVideoIsPlaying by remember { mutableStateOf(true) }
    // 用于强制刷新播放状态的触发器，当 VerticalPager 切换页面时使用
    var playbackStateRefreshTrigger by remember { mutableIntStateOf(0) }
    
    // 菜单相关状态
    var isMenuVisible by remember { mutableStateOf(false) }
    
    // 获取当前活动上下文
    val context = LocalContext.current
    val activity = context as? Activity

    // 监控菜单显示状态，并在菜单关闭时手动控制状态栏
    LaunchedEffect(isMenuVisible, isStatusBarVisible) {
        if (!isMenuVisible && activity != null) {
            // 菜单关闭时，根据当前的isStatusBarVisible状态设置状态栏
            val window = activity.window
            val controller = WindowCompat.getInsetsController(window, window.decorView)
            if (isStatusBarVisible) {
                controller.show(WindowInsetsCompat.Type.statusBars())
            } else {
                controller.hide(WindowInsetsCompat.Type.statusBars())
            }
        }
    }

    val isAppInForeground = LifecycleManager.observeAppLifecycleWithPlayback(
        playerManager = viewModel.mediaPlayerService,
        currentIndex = currentIndex
    )
    
    val pagerState = rememberPagerState(initialPage = 0) { videoList.size.coerceAtLeast(1) }

    LaunchedEffect(pagerState.currentPage, videoList) { // 当页面或视频列表变化时
        if (videoList.isNotEmpty() && pagerState.currentPage < videoList.size) {
            val newIndex = pagerState.currentPage
            // 只有当用户手动滑动时才更新ViewModel的currentIndex
            // 自动播放下一首导致的currentIndex变化不应反过来触发这里的viewModel.updateCurrentIndex
            if (newIndex != currentIndex) {
                viewModel.updateCurrentIndex(newIndex)
            }
            currentVideoIsPlaying = viewModel.mediaPlayerService.getPlaybackState(newIndex) == true // 使用相等检查代替elvis
            playbackStateRefreshTrigger++ // 触发 recomposition 以确保状态更新
        }
    }

    // 监听ViewModel中currentIndex的变化，如果与pagerState不一致，则滑动到目标页面
    LaunchedEffect(currentIndex, videoList.size) { 
        if (videoList.isNotEmpty() && currentIndex < videoList.size && pagerState.currentPage != currentIndex) {
            pagerState.animateScrollToPage(currentIndex)
            // 当通过代码（如连播）切换页面后，需要重置当前页面的播放状态
            currentVideoIsPlaying = viewModel.mediaPlayerService.getPlaybackState(currentIndex) == true // 使用相等检查代替elvis
            playbackStateRefreshTrigger++
        }
    }

    // 当 currentVideoIsPlaying 状态由 VideoPlayerComponent 回调更新时，确保 viewModel 中的状态也同步
    // 这个 LaunchedEffect 确保当用户在当前页面点暂停/播放时，状态能同步回 MediaPlayerService
    LaunchedEffect(currentVideoIsPlaying, currentIndex) {
        viewModel.mediaPlayerService.savePlaybackState(currentIndex, currentVideoIsPlaying)
    }

    Box(modifier = modifier.fillMaxSize()) {
        VerticalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            val isCurrentPage = page == pagerState.currentPage
            val isVisible = pagerState.layoutInfo.visiblePagesInfo.any { it.index == page } && isAppInForeground
            
            // 使用Box包装视频内容和按钮，这样按钮会跟随视频一起滚动
            Box(modifier = Modifier.fillMaxSize()) {
            if (videoList.isNotEmpty() && page < videoList.size) {
                VideoPlayerComponent(
                    video = videoList[page],
                    playerManager = viewModel.mediaPlayerService,
                    pageIndex = page,
                    isCurrentItem = isCurrentPage,
                    isVisible = isVisible,
                    displayMode = displayMode,
                    onIsPlayingChanged = {
                        if (isCurrentPage) { // 只更新当前页面的播放状态
                            currentVideoIsPlaying = it
                        }
                    },
                    onLongPress = { // Pass the long press callback directly
                        isMenuVisible = true
                    }
                )
            } else {
                Box(Modifier.fillMaxSize().background(Color.Black))
            }
                
                // 控制按钮的可见性逻辑
                val shouldShowButtons = if (isBottomBarVisible) {
                    true // 如果导航栏可见，按钮始终可见
                } else {
                    !currentVideoIsPlaying // 如果导航栏隐藏，则仅当视频暂停时显示按钮
                }
                
                // 只在当前页面且满足显示条件时才显示按钮
                if (isCurrentPage && shouldShowButtons && isVisible) {
        val bottomNavBarHeight = 48.dp
        val controlsBottomPadding = if (isBottomBarVisible) {
            16.dp + bottomNavBarHeight
        } else {
            // 导航栏隐藏时
            if (!currentVideoIsPlaying) { // 视频暂停时（显示按钮）
                // 提高按钮位置以避免与进度条重叠
                48.dp 
            } else { // 视频播放时（隐藏按钮）
            16.dp
            }
        }

                    VideoControlButtons(
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(end = 16.dp, bottom = controlsBottomPadding),
                        currentVideo = if (videoList.isNotEmpty() && currentIndex < videoList.size) videoList[currentIndex] else null,
                        isFavorite = currentVideoIsFavorite,
                        onFavoriteClick = { viewModel.toggleCurrentVideoFavorite() },
                        onDownloadClick = { /* 下载功能 */ },
                        onShareClick = { /* 分享功能 */ },
                        onMenuClick = { isMenuVisible = true },
                        onToggleBottomBarVisibility = toggleBottomBarVisibility,
                        isBottomBarVisible = isBottomBarVisible,
                        showToggleButton = true,
                        showMenuButton = true
                    )
                }
            }
        }
        
        // 底部菜单 - 使用Dialog形式的BottomMenuSheet确保显示在导航栏上层
        BottomMenuSheet(
            isVisible = isMenuVisible,
            onDismiss = { isMenuVisible = false },
            currentDisplayMode = displayMode,
            onToggleDisplayMode = { viewModel.toggleVideoDisplayMode() },
            isAutoPlayNextEnabled = autoPlayNextEnabled,
            onToggleAutoPlayNext = { viewModel.toggleAutoPlayNextEnabled() },
            isStatusBarVisible = isStatusBarVisible
        )
    }
} 