package com.example.tikoppij.utils

/**
 * 应用常量配置
 * 统一管理项目中的所有常量定义
 */
object Constants {
    
    // =================== 视频加载相关 ===================
    object Video {
        /** 初始加载视频数量 */
        const val INITIAL_LOAD_COUNT = 3
        
        /** 每次加载更多的视频数量 */
        const val LOAD_MORE_COUNT = 10
        
        /** 触发加载更多的阈值 */
        const val LOAD_MORE_THRESHOLD = 5
    }
    
    // =================== 缓存管理相关 ===================
    object Cache {
        /** 默认最大缓存大小 (300MB) */
        const val DEFAULT_MAX_CACHE_SIZE = 300 * 1024 * 1024L
        
        /** 最小缓存大小 (500MB) */
        const val MIN_CACHE_SIZE = 500 * 1024 * 1024L
        
        /** 最大缓存大小 (4GB) */
        const val MAX_CACHE_SIZE = 4 * 1024 * 1024 * 1024L
        
        /** 最小可播放缓存大小 (500KB) */
        const val MIN_PLAYABLE_CACHE_SIZE = 500 * 1024L
        
        /** 缓存目录名称 */
        const val CACHE_DIRECTORY_NAME = "exoplayer_cache"
        
        /** 最小缓存阈值 (1KB) */
        const val MIN_CACHE_THRESHOLD = 1024L
    }
    
    // =================== 网络相关 ===================
    object Network {
        /** API基础URL */
        const val BASE_URL = "http://ssb.yyywu.com/"
        
        /** 默认分类ID */
        const val DEFAULT_CATEGORY = 99
        
        /** 默认请求数量 */
        const val DEFAULT_COUNT = 10
        
        /** 视频连接超时时间（秒） */
        const val VIDEO_CONNECT_TIMEOUT = 15
        
        /** 视频读取超时时间（秒） */
        const val VIDEO_READ_TIMEOUT = 30
        
        /** 视频写入超时时间（秒） */
        const val VIDEO_WRITE_TIMEOUT = 30
        
        /** API连接超时时间（秒） */
        const val API_CONNECT_TIMEOUT = 10
        
        /** API读取超时时间（秒） */
        const val API_READ_TIMEOUT = 10
        
        /** API写入超时时间（秒） */
        const val API_WRITE_TIMEOUT = 10
    }
    
    // =================== 数据存储相关 ===================
    object Storage {
        /** 最大历史记录数量 */
        const val MAX_HISTORY_COUNT = 50
    }
    
    // =================== 性能监控相关 ===================
    object Performance {
        /** 性能监控日志标签 */
        const val MONITOR_TAG = "PerformanceMonitor"
        
        /** 是否启用性能监控日志 */
        val ENABLE_LOGGING = true
    }
    
    // =================== 时间相关 ===================
    object Time {
        /** 一分钟的毫秒数 */
        const val MINUTE_IN_MILLIS = 60 * 1000L
        
        /** 一小时的毫秒数 */
        const val HOUR_IN_MILLIS = 60 * 60 * 1000L
        
        /** 一天的毫秒数 */
        const val DAY_IN_MILLIS = 24 * 60 * 60 * 1000L
        
        /** 一周的毫秒数 */
        const val WEEK_IN_MILLIS = 7 * 24 * 60 * 60 * 1000L
        
        /** 一秒的毫秒数 */
        const val SECOND_IN_MILLIS = 1000L
    }
    
    // =================== 文件大小相关 ===================
    object FileSize {
        /** 字节转换常量 */
        const val BYTES_PER_KB = 1024.0
        const val BYTES_PER_MB = 1024L * 1024L
    }
} 