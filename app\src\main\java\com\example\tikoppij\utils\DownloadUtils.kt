package com.example.tikoppij.utils

import android.app.DownloadManager
import android.content.Context
import android.net.Uri
import android.os.Environment
import kotlin.random.Random

/**
 * 下载工具类
 * 处理视频下载相关功能
 */
object DownloadUtils {
    
    /**
     * 生成随机文件名
     * 格式：3个大写字母 + 3个小写字母 + .mp4
     */
    fun generateRandomFileName(): String {
        val upperCaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        val lowerCaseLetters = "abcdefghijklmnopqrstuvwxyz"
        
        val upperPart = (1..3).map { upperCaseLetters.random() }.joinToString("")
        val lowerPart = (1..3).map { lowerCaseLetters.random() }.joinToString("")
        
        return "${upperPart}${lowerPart}.mp4"
    }
    
    /**
     * 下载视频文件
     * @param context 上下文
     * @param videoUrl 视频URL
     * @param fileName 文件名（可选，如果为空则自动生成）
     * @return 下载ID
     */
    fun downloadVideo(context: Context, videoUrl: String, fileName: String? = null): Long {
        val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        
        val finalFileName = fileName ?: generateRandomFileName()
        
        val request = DownloadManager.Request(Uri.parse(videoUrl)).apply {
            setTitle("视频下载")
            setDescription("正在下载视频: $finalFileName")
            setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
            setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, finalFileName)
            setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI or DownloadManager.Request.NETWORK_MOBILE)
            setAllowedOverRoaming(false)
        }
        
        return downloadManager.enqueue(request)
    }
} 