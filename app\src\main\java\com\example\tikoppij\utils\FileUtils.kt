package com.example.tikoppij.utils

import java.text.DecimalFormat

/**
 * 文件工具类
 * 提供文件相关的通用工具方法
 */
object FileUtils {
    
    /**
     * 格式化文件大小
     * @param size 文件大小（字节）
     * @return 格式化后的文件大小字符串
     */
    fun formatFileSize(size: Long): String {
        val df = DecimalFormat("#.##")
        val sizeKb = size / Constants.FileSize.BYTES_PER_KB
        val sizeMb = sizeKb / Constants.FileSize.BYTES_PER_KB
        val sizeGb = sizeMb / Constants.FileSize.BYTES_PER_KB
        
        return when {
            sizeGb >= 1 -> "${df.format(sizeGb)}GB"
            sizeMb >= 1 -> "${df.format(sizeMb)}MB"
            sizeKb >= 1 -> "${df.format(sizeKb)}KB"
            else -> "${size}B"
        }
    }
} 