package com.example.tikoppij.utils

import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Environment
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 内部下载管理器 - 使用Android官方DownloadManager
 * 这是Android官方推荐的文件下载方式
 */
object InternalDownloadManager {
    
    private var downloadReceiver: DownloadReceiver? = null
    
    /**
     * 使用Android系统DownloadManager下载视频
     * 这是官方推荐的方式，系统会自动处理权限、进度、通知等
     */
    suspend fun downloadVideo(
        context: Context,
        videoUrl: String,
        fileName: String? = null,
        onProgress: (downloaded: Long, total: Long) -> Unit = { _, _ -> },
        onComplete: (success: Boolean, result: String) -> Unit
    ) = withContext(Dispatchers.Main) {
        
        try {
            val finalFileName = fileName ?: generateRandomFileName()
            Log.d("InternalDownload", "开始下载视频: $videoUrl")
            Log.d("InternalDownload", "生成文件名: $finalFileName")
            
            // 获取系统DownloadManager
            val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
            
            // 创建下载请求
            val request = DownloadManager.Request(Uri.parse(videoUrl))
                .setTitle("Tikoppij 视频下载")
                .setDescription("正在下载视频...")
                .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
                .setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, "TikoppiVideos/$finalFileName")
                .setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI or DownloadManager.Request.NETWORK_MOBILE)
                .setAllowedOverRoaming(false)
            
            // 开始下载
            val downloadId = downloadManager.enqueue(request)
            Log.d("InternalDownload", "下载ID: $downloadId")
            
            // 注册下载完成广播接收器
            downloadReceiver = DownloadReceiver(downloadId, onComplete)
            val filter = IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE)
            context.registerReceiver(downloadReceiver, filter)
            
            onComplete(true, "下载已开始，请查看通知栏...")
            
        } catch (e: Exception) {
            Log.e("InternalDownload", "下载异常: ${e.message}", e)
            onComplete(false, "下载启动失败: ${e.message}")
        }
    }
    
    /**
     * 生成随机文件名
     */
    private fun generateRandomFileName(): String {
        val upperChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        val lowerChars = "abcdefghijklmnopqrstuvwxyz"
        
        val randomUpper = (1..3).map { upperChars.random() }.joinToString("")
        val randomLower = (1..3).map { lowerChars.random() }.joinToString("")
        
        return "$randomUpper$randomLower.mp4"
    }
    
    /**
     * 清理资源
     */
    fun cleanup(context: Context) {
        downloadReceiver?.let { receiver ->
            try {
                context.unregisterReceiver(receiver)
                downloadReceiver = null
            } catch (e: Exception) {
                Log.e("InternalDownload", "清理广播接收器失败: ${e.message}")
            }
        }
    }
    
    /**
     * 下载完成广播接收器
     */
    private class DownloadReceiver(
        private val downloadId: Long,
        private val onComplete: (success: Boolean, result: String) -> Unit
    ) : BroadcastReceiver() {
        
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == DownloadManager.ACTION_DOWNLOAD_COMPLETE) {
                val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
                
                if (id == downloadId) {
                    Log.d("InternalDownload", "下载完成，ID: $id")
                    
                    val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
                    val query = DownloadManager.Query().setFilterById(id)
                    val cursor = downloadManager.query(query)
                    
                    if (cursor.moveToFirst()) {
                        val statusIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS)
                        val status = cursor.getInt(statusIndex)
                        
                        when (status) {
                            DownloadManager.STATUS_SUCCESSFUL -> {
                                onComplete(true, "下载成功！文件已保存到下载文件夹")
                            }
                            DownloadManager.STATUS_FAILED -> {
                                val reasonIndex = cursor.getColumnIndex(DownloadManager.COLUMN_REASON)
                                val reason = cursor.getInt(reasonIndex)
                                onComplete(false, "下载失败，错误代码: $reason")
                            }
                            else -> {
                                onComplete(false, "下载状态异常: $status")
                            }
                        }
                    } else {
                        onComplete(false, "无法查询下载状态")
                    }
                    
                    cursor.close()
                    
                    // 清理广播接收器
                    cleanup(context)
                }
            }
        }
    }
} 