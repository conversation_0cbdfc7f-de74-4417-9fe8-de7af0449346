package com.example.tikoppij.utils

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.video.MediaPlayerService

/**
 * 生命周期管理工具类
 * 统一处理应用生命周期监听和视频播放控制
 */
@UnstableApi
object LifecycleManager {

    /**
     * 监听应用生命周期并处理视频播放控制
     * @param playerManager 播放器服务
     * @param currentIndex 当前播放索引
     * @param lifecycleOwner 生命周期拥有者
     * @return 是否在前台
     */
    @Composable
    fun observeAppLifecycleWithPlayback(
        playerManager: MediaPlayerService,
        currentIndex: Int,
        lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current
    ): Boolean {
        // 应用前台状态
        val isAppInForeground = rememberSaveable { mutableStateOf(true) }
        
        // 监听处理器引用
        val playerAndIndexRef = remember { 
            mutableStateOf(Pair(playerManager, currentIndex)) 
        }
        
        // 更新引用值（确保使用最新值）
        playerAndIndexRef.value = Pair(playerManager, currentIndex)
        
        // 生命周期监听
        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                val (player, index) = playerAndIndexRef.value
                
                when (event) {
                    Lifecycle.Event.ON_RESUME -> {
                        isAppInForeground.value = true
                        // 前台时自动恢复播放由各组件自行决定
                    }
                    Lifecycle.Event.ON_PAUSE -> {
                        isAppInForeground.value = false
                        // 后台时强制暂停播放
                        if (index >= 0) {
                            player.pause(index)
                        }
                    }
                    else -> { /* 其他事件不处理 */ }
                }
            }
            
            lifecycleOwner.lifecycle.addObserver(observer)
            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }
        
        return isAppInForeground.value
    }
} 