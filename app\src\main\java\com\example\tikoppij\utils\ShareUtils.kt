package com.example.tikoppij.utils

import android.content.Context
import android.content.Intent

/**
 * 分享工具类
 * 处理内容分享相关功能
 */
object ShareUtils {
    
    /**
     * 分享文本内容
     * @param context 上下文
     * @param text 要分享的文本
     * @param title 分享标题
     */
    fun shareText(context: Context, text: String, title: String = "分享") {
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, text)
        }
        
        val chooserIntent = Intent.createChooser(shareIntent, title)
        context.startActivity(chooserIntent)
    }
    
    /**
     * 分享视频信息
     * @param context 上下文
     * @param videoId 视频ID
     * @param videoUrl 视频URL
     */
    fun shareVideoInfo(context: Context, videoId: String, videoUrl: String) {
        val shareText = "我发现了一个很棒的视频！\n\n视频ID: $videoId\n视频链接: $videoUrl\n\n快来一起观看吧！"
        shareText(context, shareText, "分享视频")
    }
} 