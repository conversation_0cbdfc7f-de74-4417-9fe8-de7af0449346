package com.example.tikoppij.video

import android.content.Context
import androidx.core.net.toUri
import androidx.media3.common.util.UnstableApi
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.cache.Cache
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.datasource.okhttp.OkHttpDataSource
import com.example.tikoppij.MyApplication
import com.example.tikoppij.network.NetworkProvider
import com.example.tikoppij.utils.Constants
import com.example.tikoppij.utils.FileUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 视频缓存管理器
 * 负责处理ExoPlayer的缓存功能
 */
@UnstableApi
class CacheManager(private val context: Context) {
    
    // 缓存配置常量
    private var maxCacheSizeBytes = Constants.Cache.DEFAULT_MAX_CACHE_SIZE
    private val minPlayableCacheSizeBytes = Constants.Cache.MIN_PLAYABLE_CACHE_SIZE
    
    // 缓存实例
    private var cache: SimpleCache? = null
    
    // 缓存数据源工厂
    private var cacheDataSourceFactory: CacheDataSource.Factory? = null
    
    // 缓存驱逐器
    private var evictor: LeastRecentlyUsedCacheEvictor? = null
    
    init {
        // 从用户偏好中读取缓存大小设置
        initCacheSize()
    }
    
    /**
     * 初始化缓存大小
     */
    private fun initCacheSize() {
        try {
            // 使用runBlocking在初始化时同步获取缓存大小设置
            runBlocking {
                val savedCacheSize = MyApplication.userPreferencesRepository.maxCacheSize.first()
                maxCacheSizeBytes = savedCacheSize.coerceIn(Constants.Cache.MIN_CACHE_SIZE, Constants.Cache.MAX_CACHE_SIZE)
            }
        } catch (_: Exception) {
            // 如果获取失败，使用默认值
            maxCacheSizeBytes = Constants.Cache.DEFAULT_MAX_CACHE_SIZE
        }
    }
    
    /**
     * 获取缓存实例
     */
    @Synchronized
    fun getCache(): Cache {
        if (cache == null) {
            val cacheDir = File(context.cacheDir, Constants.Cache.CACHE_DIRECTORY_NAME)
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            val databaseProvider = StandaloneDatabaseProvider(context)
            evictor = LeastRecentlyUsedCacheEvictor(maxCacheSizeBytes)

            cache = SimpleCache(cacheDir, evictor!!, databaseProvider)
        }

        return cache!!
    }
    
    /**
     * 获取缓存数据源工厂
     */
    @Synchronized
    fun getCacheDataSourceFactory(): CacheDataSource.Factory {
        if (cacheDataSourceFactory == null) {
            val cache = getCache()

            val okHttpClient = NetworkProvider.videoHttpClient
            val upstreamFactory = DefaultDataSource.Factory(
                context,
                OkHttpDataSource.Factory(okHttpClient)
            )

            cacheDataSourceFactory = CacheDataSource.Factory()
                .setCache(cache)
                .setUpstreamDataSourceFactory(upstreamFactory)
                .setFlags(
                    CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR or
                    CacheDataSource.FLAG_BLOCK_ON_CACHE
                )
        }

        return cacheDataSourceFactory!!
    }
    
    /**
     * 检查URL缓存状态
     * @param url 视频URL
     * @return 是否已缓存足够播放的部分
     */
    fun checkCacheStatus(url: String): Boolean {
        val cacheInstance = cache ?: return false
        val uri = url.toUri()

        return try {
            cacheInstance.isCached(uri.toString(), 0L, minPlayableCacheSizeBytes)
        } catch (_: Exception) {
            false
        }
    }
    
    /**
     * 检查URL是否已缓存足够播放的部分
     */
    fun isCachedForPlayback(url: String): Boolean {
        return checkCacheStatus(url)
    }
    
    /**
     * 获取当前缓存大小（字节）
     */
    fun getCurrentCacheSize(): Long {
        val cacheDir = File(context.cacheDir, Constants.Cache.CACHE_DIRECTORY_NAME)
        // 如果目录不存在或者大小为0，立即返回0
        if (!cacheDir.exists() || cacheDir.listFiles()?.isEmpty() == true) {
            return 0L
        }
        
        // 计算实际大小
        val size = calculateDirSize(cacheDir)
        // 如果实际大小非常小（<1KB），也视为0
        return if (size < Constants.Cache.MIN_CACHE_THRESHOLD) 0L else size
    }
    
    /**
     * 获取当前缓存大小（格式化字符串）
     */
    fun getCurrentCacheSizeFormatted(): String {
        val size = getCurrentCacheSize()
        return FileUtils.formatFileSize(size)
    }
    
    /**
     * 获取最大缓存大小（字节）
     */
    fun getMaxCacheSize(): Long {
        return maxCacheSizeBytes
    }
    
    /**
     * 获取最大缓存大小（格式化字符串）
     */
    fun getMaxCacheSizeFormatted(): String {
        return FileUtils.formatFileSize(maxCacheSizeBytes)
    }
    
    /**
     * 设置最大缓存大小
     * @param newMaxSize 新的最大缓存大小（字节）
     */
    suspend fun setMaxCacheSize(newMaxSize: Long) {
        val validSize = newMaxSize.coerceIn(Constants.Cache.MIN_CACHE_SIZE, Constants.Cache.MAX_CACHE_SIZE)
        
        // 保存到用户偏好
        MyApplication.userPreferencesRepository.updateMaxCacheSize(validSize)
        
        // 更新内存中的值
        maxCacheSizeBytes = validSize
        
        // 更新驱逐器
        withContext(Dispatchers.IO) {
            val currentCache = cache
            if (currentCache != null) {
                // 需要重新创建缓存和驱逐器
                val cacheDir = File(context.cacheDir, Constants.Cache.CACHE_DIRECTORY_NAME)
                val databaseProvider = StandaloneDatabaseProvider(context)
                
                try {
                    // 释放旧缓存
                    currentCache.release()
                    
                    // 创建新的驱逐器和缓存
                    evictor = LeastRecentlyUsedCacheEvictor(validSize)
                    cache = SimpleCache(cacheDir, evictor!!, databaseProvider)
                    
                    // 重置缓存数据源工厂
                    cacheDataSourceFactory = null
                } catch (_: Exception) {
                    // 错误处理
                }
            }
        }
    }
    
    /**
     * 清理所有缓存
     */
    suspend fun clearAllCache(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val currentCache = cache
                if (currentCache != null) {
                    // 不释放缓存实例，而是清理所有缓存内容
                    try {
                        // 移除所有缓存的键
                        val cacheKeys = currentCache.keys
                        for (key in cacheKeys) {
                            currentCache.removeResource(key)
                        }
                    } catch (_: Exception) {
                        // 如果上述方法失败，尝试删除缓存目录内容
                        val cacheDir = File(context.cacheDir, Constants.Cache.CACHE_DIRECTORY_NAME)
                        if (cacheDir.exists()) {
                            // 删除目录内容但保留目录本身
                            cacheDir.listFiles()?.forEach { file ->
                                if (file.isDirectory) {
                                    deleteDir(file)
                                } else {
                                    file.delete()
                                }
                            }
                        }
                    }
                } else {
                    // 如果缓存实例不存在，直接删除缓存目录内容
                    val cacheDir = File(context.cacheDir, Constants.Cache.CACHE_DIRECTORY_NAME)
                    if (cacheDir.exists()) {
                        cacheDir.listFiles()?.forEach { file ->
                            if (file.isDirectory) {
                                deleteDir(file)
                            } else {
                                file.delete()
                            }
                        }
                    }
                }
                
                true
            } catch (_: Exception) {
                false
            }
        }
    }
    
    /**
     * 递归计算目录大小
     */
    private fun calculateDirSize(dir: File): Long {
        var size = 0L
        
        try {
            val files = dir.listFiles() ?: return 0
            
            for (file in files) {
                size += if (file.isDirectory) {
                    calculateDirSize(file)
                } else {
                    file.length()
                }
            }
        } catch (_: Exception) {
            // 忽略异常
        }
        
        return size
    }
    
    /**
     * 递归删除目录
     */
    private fun deleteDir(dir: File): Boolean {
        try {
            val files = dir.listFiles() ?: return dir.delete()
            
            for (file in files) {
                if (file.isDirectory) {
                    deleteDir(file)
                } else {
                    file.delete()
                }
            }
            
            return dir.delete()
        } catch (_: Exception) {
            return false
        }
    }

}