package com.example.tikoppij.video

import android.content.Context
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.utils.PerformanceMonitor

/**
 * 统一媒体播放器服务
 * 作为协调层，整合CacheManager和PlayerPoolManager的功能
 */
@UnstableApi
class MediaPlayerService(context: Context) {
    
    // 组件实例
    private val cacheManager = CacheManager(context)
    private val playerPoolManager = PlayerPoolManager(context, cacheManager)
    
    // 视频结束回调代理
    var onVideoEnded: ((pageIndex: Int) -> Unit)? = null
        set(value) {
            field = value
            playerPoolManager.onVideoEnded = value
        }
    
    init {
        PerformanceMonitor.recordTimePoint("MediaPlayerService.init")
    }
    
    /**
     * 获取或创建视频播放器
     */
    fun getOrCreatePlayer(video: VideoModel, pageIndex: Int): ExoPlayer {
        return playerPoolManager.getOrCreatePlayer(video, pageIndex)
    }
    
    /**
     * 播放视频
     */
    fun play(pageIndex: Int) {
        playerPoolManager.play(pageIndex)
    }
    
    /**
     * 暂停视频
     */
    fun pause(pageIndex: Int, updateActiveStatus: Boolean = true) {
        playerPoolManager.pause(pageIndex, updateActiveStatus)
    }
    
    /**
     * 初始化播放器
     */
    fun initializePlayers(videoList: List<VideoModel>, currentIndex: Int = 0) {
        playerPoolManager.initializePlayers(videoList, currentIndex)
    }
    
    /**
     * 更新播放器活跃状态
     */
    fun updateActiveState(pageIndex: Int, isActive: Boolean) {
        playerPoolManager.updateActiveState(pageIndex, isActive)
    }
    
    /**
     * 停止指定页面的播放器
     */
    fun stopPlayerForPage(pageIndex: Int) {
        playerPoolManager.stopPlayerForPage(pageIndex)
    }
    
    /**
     * 根据页面索引获取播放器
     */
    fun getPlayerByIndex(pageIndex: Int): ExoPlayer? {
        return playerPoolManager.getPlayerByIndex(pageIndex)
    }
    
    /**
     * 保存指定页面的播放状态
     */
    fun savePlaybackState(pageIndex: Int, isPlaying: Boolean) {
        playerPoolManager.savePlaybackState(pageIndex, isPlaying)
    }
    
    /**
     * 获取指定页面的播放状态
     */
    fun getPlaybackState(pageIndex: Int): Boolean? {
        return playerPoolManager.getPlaybackState(pageIndex)
    }
    
    // =================== 缓存管理功能 ===================
    
    /**
     * 获取当前缓存大小（字节）
     */
    fun getCurrentCacheSize(): Long {
        return cacheManager.getCurrentCacheSize()
    }
    
    /**
     * 获取当前缓存大小（格式化字符串）
     */
    fun getCurrentCacheSizeFormatted(): String {
        return cacheManager.getCurrentCacheSizeFormatted()
    }
    
    /**
     * 获取最大缓存大小（字节）
     */
    fun getMaxCacheSize(): Long {
        return cacheManager.getMaxCacheSize()
    }
    
    /**
     * 获取最大缓存大小（格式化字符串）
     */
    fun getMaxCacheSizeFormatted(): String {
        return cacheManager.getMaxCacheSizeFormatted()
    }
    
    /**
     * 设置最大缓存大小
     * @param newMaxSize 新的最大缓存大小（字节）
     */
    suspend fun setMaxCacheSize(newMaxSize: Long) {
        cacheManager.setMaxCacheSize(newMaxSize)
    }
    
    /**
     * 清理所有缓存
     */
    suspend fun clearAllCache(): Boolean {
        return cacheManager.clearAllCache()
    }

    /**
     * 清理播放器池
     * 用于切换到新的视频列表时清理旧的播放器状态
     */
    fun clearPlayerPool() {
        playerPoolManager.clearPlayerPool()
    }
}