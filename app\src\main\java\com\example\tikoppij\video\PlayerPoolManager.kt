package com.example.tikoppij.video

import android.content.Context
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.DefaultRenderersFactory
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.upstream.DefaultAllocator
import com.example.tikoppij.model.VideoModel
import java.util.concurrent.ConcurrentHashMap

/**
 * 播放器池管理器
 * 负责管理ExoPlayer实例池和播放状态
 */
@UnstableApi
class PlayerPoolManager(
    private val context: Context,
    private val cacheManager: CacheManager
) {
    // 固定大小为3的播放器槽位
    private val playerSlotCount = 3
    private val playerSlots = arrayOfNulls<PlayerState>(playerSlotCount)
    
    // 保存每个页面的播放状态（true=播放，false=暂停）
    private val playbackStateMap = ConcurrentHashMap<Int, Boolean>()
    
    // 视频结束回调
    var onVideoEnded: ((pageIndex: Int) -> Unit)? = null
    
    private data class PlayerState(
        val player: ExoPlayer,
        var videoId: String? = null,
        var pageIndex: Int = -1,
        var isActive: Boolean = false
    )
    
    /**
     * 获取或创建视频播放器
     * @param video 视频数据
     * @param pageIndex 视频在列表中的实际索引位置
     * @return ExoPlayer实例
     */
    fun getOrCreatePlayer(video: VideoModel, pageIndex: Int): ExoPlayer {
        if (pageIndex < 0) throw IllegalArgumentException("Page index cannot be negative")
        
        val slotIndex = getSlotIndexForPage(pageIndex)
        val existingState = playerSlots[slotIndex]
        
        return existingState?.let { 
            handleExistingPlayer(it, video, pageIndex) 
        } ?: createNewPlayer(video, pageIndex, slotIndex)
    }

    /**
     * 处理已存在的播放器
     */
    private fun handleExistingPlayer(existingState: PlayerState, video: VideoModel, pageIndex: Int): ExoPlayer {
        return when {
            isExactMatch(existingState, video, pageIndex) -> existingState.player
            isSamePageDifferentVideo(existingState, video, pageIndex) -> {
                updatePlayerMedia(existingState, video)
            }
            else -> reusePlayerForNewPage(existingState, video, pageIndex)
        }
    }

    /**
     * 检查是否完全匹配
     */
    private fun isExactMatch(state: PlayerState, video: VideoModel, pageIndex: Int): Boolean {
        return state.pageIndex == pageIndex && state.videoId == video.video_id
    }

    /**
     * 检查是否相同页面但不同视频
     */
    private fun isSamePageDifferentVideo(state: PlayerState, video: VideoModel, pageIndex: Int): Boolean {
        return state.pageIndex == pageIndex && state.videoId != video.video_id
    }

    /**
     * 更新播放器媒体内容
     */
    private fun updatePlayerMedia(state: PlayerState, video: VideoModel): ExoPlayer {
        val mediaItem = MediaItem.fromUri(video.url)
        state.player.setMediaItem(mediaItem)
        state.player.prepare()
        state.videoId = video.video_id
        return state.player
    }

    /**
     * 重用播放器用于新页面
     */
    private fun reusePlayerForNewPage(state: PlayerState, video: VideoModel, pageIndex: Int): ExoPlayer {
        val mediaItem = MediaItem.fromUri(video.url)
        state.player.setMediaItem(mediaItem)
        state.player.prepare()
        
        // 更新状态
        state.videoId = video.video_id
        state.pageIndex = pageIndex
        state.isActive = false
        
        return state.player
    }

    /**
     * 创建新播放器
     */
    private fun createNewPlayer(video: VideoModel, pageIndex: Int, slotIndex: Int): ExoPlayer {
        val newPlayer = createPlayer(video)
        playerSlots[slotIndex] = PlayerState(
            player = newPlayer,
            videoId = video.video_id,
            pageIndex = pageIndex,
            isActive = false
        )
        return newPlayer
    }
    
    /**
     * 创建播放器
     * @param video 视频数据
     * @return 播放器实例
     */
    private fun createPlayer(video: VideoModel): ExoPlayer {
        // 获取所需组件
        val isCached = cacheManager.isCachedForPlayback(video.url)
        val cacheDataSourceFactory = cacheManager.getCacheDataSourceFactory()
        val mediaSourceFactory = DefaultMediaSourceFactory(cacheDataSourceFactory)
        
        // 创建自定义加载控制器，优化缓冲策略
        val loadControl = DefaultLoadControl.Builder()
            .setAllocator(DefaultAllocator(true, 8 * 1024 * 1024))
            .setBufferDurationsMs(
                2000,  // minBufferMs
                50000, // maxBufferMs
                1000,  // bufferForPlaybackMs
                2000   // bufferForPlaybackAfterRebufferMs
            )
            .setPrioritizeTimeOverSizeThresholds(true)
            .build()
        
        // 创建硬件加速优先的渲染器工厂
        val renderersFactory = DefaultRenderersFactory(context)
            .setExtensionRendererMode(DefaultRenderersFactory.EXTENSION_RENDERER_MODE_PREFER)
            .experimentalSetEnableMediaCodecVideoRendererPrewarming(true) // 启用预热
        
        // 创建播放器
        val player = ExoPlayer.Builder(context)
            .setRenderersFactory(renderersFactory)
            .setMediaSourceFactory(mediaSourceFactory)
            .setLoadControl(loadControl)
            .setReleaseTimeoutMs(5000)
            .build()
        
        // 设置初始播放状态
        player.playWhenReady = false
        
        // 设置循环播放
        player.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(state: Int) {
                if (state == Player.STATE_ENDED) {
                    // 当视频播放结束时，调用回调函数，而不是直接重播
                    val playerState = playerSlots.find { it?.player == player }
                    playerState?.pageIndex?.let { pageIndex ->
                        onVideoEnded?.invoke(pageIndex)
                    }
                }
            }
        })
        
        // 设置媒体项
        player.setMediaItem(MediaItem.Builder()
            .setUri(video.url)
            .setMediaId(video.video_id)
            .build())
        
        // 准备播放器
        player.prepare()
        
        // 如果已缓存，设置播放位置为0
        if (isCached) {
            player.seekTo(0)
        }
        
        return player
    }
    
    /**
     * 播放视频
     * @param pageIndex 页面索引
     */
    fun play(pageIndex: Int) {
        if (pageIndex < 0) return
        
        val slotIndex = getSlotIndexForPage(pageIndex)
        playerSlots[slotIndex]?.let { state ->
            if (state.pageIndex == pageIndex) {
                state.player.playWhenReady = true
                state.player.play()
                state.isActive = true
            }
        }
    }
    
    /**
     * 暂停视频
     * @param pageIndex 页面索引
     * @param updateActiveStatus 是否更新活跃状态
     */
    fun pause(pageIndex: Int, updateActiveStatus: Boolean = true) {
        if (pageIndex < 0) return
        
        val slotIndex = getSlotIndexForPage(pageIndex)
        playerSlots[slotIndex]?.let { state ->
            if (state.pageIndex == pageIndex) {
                // 立即设置playWhenReady为false，这是暂停的关键标志
                state.player.playWhenReady = false
                
                // 确保调用pause，防止播放器状态不一致
                if (state.player.isPlaying) {
                    state.player.pause()
                }
                
                // 有条件地更新活跃状态（用于UI控制）
                if (updateActiveStatus) {
                    state.isActive = false
                }
            }
        }
    }
    
    /**
     * 根据页面索引获取槽位索引
     */
    private fun getSlotIndexForPage(pageIndex: Int): Int {
        return pageIndex.coerceAtLeast(0) % playerSlotCount
    }
    
    /**
     * 初始化播放器
     * 预先为当前和相邻槽位的播放器准备实例
     * @param videoList 视频列表
     * @param currentIndex 当前页面索引
     */
    fun initializePlayers(videoList: List<VideoModel>, currentIndex: Int = 0) {
        if (videoList.isEmpty()) return

        val validCurrentIndex = currentIndex.coerceIn(videoList.indices)

        // 按优先级顺序准备播放器：当前页 -> 下一页 -> 上一页
        val indicesToPrepare = listOf(
            validCurrentIndex,
            (validCurrentIndex + 1).coerceAtMost(videoList.size - 1),
            (validCurrentIndex - 1).coerceAtLeast(0)
        ).distinct()

        indicesToPrepare.forEach { pageIndex ->
            try {
                val video = videoList.getOrNull(pageIndex) ?: return@forEach
                val slotIndex = getSlotIndexForPage(pageIndex)
                
                // 如果槽位被占用且不是当前要设置的页面，检查是否应该跳过
                val existingState = playerSlots[slotIndex]
                if (shouldSkipSlot(existingState, pageIndex, validCurrentIndex)) {
                    return@forEach
                }
                
                getOrCreatePlayer(video, pageIndex)
            } catch (_: Exception) {
                // 处理异常
            }
        }
    }

    /**
     * 检查是否应该跳过当前槽位
     */
    private fun shouldSkipSlot(existingState: PlayerState?, pageIndex: Int, currentIndex: Int): Boolean {
        if (existingState == null || existingState.pageIndex == pageIndex) {
            return false
        }
        
        // 如果现有槽位是当前页面，不要覆盖
        return existingState.pageIndex == currentIndex
    }
    
    /**
     * 更新播放器活跃状态
     * @param pageIndex 页面索引
     * @param isActive 是否活跃
     */
    fun updateActiveState(pageIndex: Int, isActive: Boolean) {
        if (pageIndex < 0) return
        
        val slotIndex = getSlotIndexForPage(pageIndex)
        playerSlots[slotIndex]?.let { state ->
            if (state.pageIndex == pageIndex) { 
                state.isActive = isActive
            }
        }
    }
    
    /**
     * 停止指定页面的播放器
     * @param pageIndex 页面索引
     */
    fun stopPlayerForPage(pageIndex: Int) {
        // 直接调用pause方法并设置updateActiveStatus为true，避免重复逻辑
        pause(pageIndex, true)
    }
    
    /**
     * 根据页面索引获取播放器
     * @param pageIndex 页面索引
     * @return 播放器实例，如果不存在则返回null
     */
    fun getPlayerByIndex(pageIndex: Int): ExoPlayer? {
        if (pageIndex < 0) return null
        
        val slotIndex = getSlotIndexForPage(pageIndex)
        return playerSlots[slotIndex]?.let { state ->
            if (state.pageIndex == pageIndex) {
                state.player
            } else {
                null
            }
        }
    }

    /**
     * 保存指定页面的播放状态
     * @param pageIndex 页面索引
     * @param isPlaying 是否正在播放
     */
    fun savePlaybackState(pageIndex: Int, isPlaying: Boolean) {
        if (pageIndex >= 0) {
            playbackStateMap[pageIndex] = isPlaying
        }
    }
    
    /**
     * 获取指定页面的播放状态
     * @param pageIndex 页面索引
     * @return 播放状态，如果不存在则返回null
     */
    fun getPlaybackState(pageIndex: Int): Boolean? {
        return if (pageIndex >= 0) {
            playbackStateMap[pageIndex]
        } else {
            null
        }
    }

    /**
     * 清理播放器池
     * 停止所有播放器并清空槽位，用于切换到新的视频列表时
     */
    fun clearPlayerPool() {
        // 停止并释放所有播放器
        playerSlots.forEachIndexed { index, playerState ->
            if (playerState != null) {
                try {
                    playerState.player.stop()
                    playerState.player.clearMediaItems()
                } catch (_: Exception) {
                    // 处理异常
                }
            }
        }
        
        // 清空所有槽位（不释放播放器实例，只重置状态）
        for (i in playerSlots.indices) {
            playerSlots[i]?.let { playerState ->
                playerState.videoId = null
                playerState.pageIndex = -1
                playerState.isActive = false
            }
        }
        
        // 清空播放状态映射
        playbackStateMap.clear()
    }

}