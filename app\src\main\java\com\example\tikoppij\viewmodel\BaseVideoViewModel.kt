package com.example.tikoppij.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.MyApplication
import com.example.tikoppij.data.FavoriteRepository
import com.example.tikoppij.data.HistoryRepository
import com.example.tikoppij.data.UserPreferencesRepository
import com.example.tikoppij.initializer.AppInitializer
import com.example.tikoppij.model.VideoModel
import com.example.tikoppij.ui.components.VideoDisplayMode
import com.example.tikoppij.video.MediaPlayerService
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 视频相关ViewModel的基类
 * 提取公共的Repository注入、收藏状态管理、历史记录管理等逻辑
 */
@UnstableApi
abstract class BaseVideoViewModel(application: Application) : AndroidViewModel(application) {
    
    // 公共Repository实例
    protected val favoriteRepository: FavoriteRepository = MyApplication.favoriteRepository
    protected val historyRepository: HistoryRepository = MyApplication.historyRepository
    protected val userPreferencesRepository: UserPreferencesRepository = MyApplication.userPreferencesRepository
    
    // 公共MediaPlayerService实例
    val mediaPlayerService: MediaPlayerService = AppInitializer.getMediaPlayerService(application)
    
    // 视频显示模式
    private val _videoDisplayMode = MutableStateFlow(VideoDisplayMode.AUTO_ADAPT)
    val videoDisplayMode: StateFlow<VideoDisplayMode> = _videoDisplayMode.asStateFlow()

    // 自动播放下一个视频的设置
    private val _autoPlayNextEnabled = MutableStateFlow(false)
    val autoPlayNextEnabled: StateFlow<Boolean> = _autoPlayNextEnabled.asStateFlow()
    
    // 当前视频的收藏状态
    private val _currentVideoIsFavorite = MutableStateFlow(false)
    val currentVideoIsFavorite: StateFlow<Boolean> = _currentVideoIsFavorite.asStateFlow()

    // 收藏状态监听Job
    private var favoriteStatusJob: Job? = null

    init {
        // 加载显示模式设置
        viewModelScope.launch {
            userPreferencesRepository.videoDisplayMode.collectLatest { mode ->
                _videoDisplayMode.value = mode
            }
        }
        
        // 加载自动播放下一个视频设置
        viewModelScope.launch {
            userPreferencesRepository.autoPlayNextEnabled.collectLatest { enabled ->
                _autoPlayNextEnabled.value = enabled
            }
        }
    }

    /**
     * 切换视频显示模式
     */
    fun toggleVideoDisplayMode() {
        viewModelScope.launch {
            val currentMode = _videoDisplayMode.value
            val newMode = when (currentMode) {
                VideoDisplayMode.AUTO_ADAPT -> VideoDisplayMode.FIT
                VideoDisplayMode.FIT -> VideoDisplayMode.AUTO_ADAPT
            }
            userPreferencesRepository.updateVideoDisplayMode(newMode)
        }
    }

    /**
     * 切换自动播放下一个视频设置
     */
    fun toggleAutoPlayNextEnabled() {
        val newValue = !_autoPlayNextEnabled.value
        viewModelScope.launch {
            userPreferencesRepository.updateAutoPlayNextEnabled(newValue)
        }
    }

    /**
     * 切换当前视频的收藏状态
     */
    fun toggleCurrentVideoFavorite() {
        viewModelScope.launch {
            val currentVideo = getCurrentVideo()
            if (currentVideo != null) {
                if (_currentVideoIsFavorite.value) {
                    favoriteRepository.removeFavorite(currentVideo.video_id)
                } else {
                    favoriteRepository.addFavorite(currentVideo)
                }
            }
        }
    }

    /**
     * 更新当前视频的收藏状态
     */
    protected fun updateCurrentVideoFavoriteStatus(videoId: String) {
        favoriteStatusJob?.cancel()
        favoriteStatusJob = viewModelScope.launch {
            favoriteRepository.isFavorite(videoId).collectLatest { isFavorite ->
                _currentVideoIsFavorite.value = isFavorite
            }
        }
    }

    /**
     * 添加视频到历史记录
     */
    protected fun addToHistory(video: VideoModel) {
        viewModelScope.launch {
            historyRepository.addHistory(video)
        }
    }

    /**
     * 获取当前视频 - 子类需要实现
     */
    protected abstract fun getCurrentVideo(): VideoModel?

    override fun onCleared() {
        super.onCleared()
        favoriteStatusJob?.cancel()
    }
} 