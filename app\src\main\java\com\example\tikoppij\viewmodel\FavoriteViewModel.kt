package com.example.tikoppij.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.MyApplication
import com.example.tikoppij.data.FavoriteRepository
import com.example.tikoppij.model.FavoriteModel
import com.example.tikoppij.model.VideoModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 收藏列表ViewModel
 * 管理收藏视频的显示和操作
 */
@UnstableApi
class FavoriteViewModel(application: Application) : AndroidViewModel(application) {
    
    // 收藏管理仓库
    private val favoriteRepository: FavoriteRepository = MyApplication.favoriteRepository
    
    // 历史记录仓库（用于播放时记录历史）
    private val historyRepository = MyApplication.historyRepository
    
    // 收藏列表
    private val _favoriteList = MutableStateFlow<List<FavoriteModel>>(emptyList())
    val favoriteList: StateFlow<List<FavoriteModel>> = _favoriteList.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    init {
        loadFavoriteList()
    }
    
    /**
     * 加载收藏列表
     */
    private fun loadFavoriteList() {
        viewModelScope.launch {
            _isLoading.value = true
            favoriteRepository.getFavoriteList().collectLatest { favorites ->
                _favoriteList.value = favorites.sortedByDescending { it.favoriteTime } // 按收藏时间倒序排列
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 移除收藏
     */
    fun removeFavorite(videoId: String) {
        viewModelScope.launch {
            favoriteRepository.removeFavorite(videoId)
        }
    }
    
    /**
     * 清空收藏列表
     */
    fun clearFavorites() {
        viewModelScope.launch {
            favoriteRepository.clearFavorites()
        }
    }
    
    /**
     * 获取当前收藏视频列表作为VideoModel列表（用于播放）
     */
    fun getFavoriteVideosAsVideoModels(): List<VideoModel> {
        return _favoriteList.value.map { it.toVideoModel() }
    }
    
    override fun onCleared() {
        super.onCleared()
        // 清理资源（不依赖索引）
    }
} 