package com.example.tikoppij.viewmodel

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import androidx.media3.common.util.UnstableApi
import com.example.tikoppij.model.VideoModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 独立播放页面ViewModel
 * 专门用于收藏和历史列表的视频播放，避免与主页面ViewModel冲突
 */
@UnstableApi
class VideoPlayerViewModel(
    application: Application,
    initialIndex: Int = 0
) : BaseVideoViewModel(application) {
    
    // 视频列表（仅内部使用）
    private val _videoList = MutableStateFlow<List<VideoModel>>(emptyList())

    // 当前播放索引
    private val _currentIndex = MutableStateFlow(initialIndex)
    val currentIndex: StateFlow<Int> = _currentIndex.asStateFlow()
    
    // 保存原始的onVideoEnded回调
    private var originalOnVideoEnded: ((pageIndex: Int) -> Unit)? = null

    init {
        // 保存原始回调并设置新的回调
        originalOnVideoEnded = mediaPlayerService.onVideoEnded
        mediaPlayerService.onVideoEnded = { pageIndex ->
            handleVideoEnded(pageIndex)
        }
    }

    /**
     * 获取当前视频 - 实现基类的抽象方法
     */
    override fun getCurrentVideo(): VideoModel? {
        val videos = _videoList.value
        val index = _currentIndex.value
        return if (videos.isNotEmpty() && index >= 0 && index < videos.size) {
            videos[index]
        } else {
            null
        }
    }

    /**
     * 处理视频播放结束事件
     */
    private fun handleVideoEnded(pageIndex: Int) {
        // 只处理当前页面的视频结束事件
        if (pageIndex == _currentIndex.value) {
            val videos = _videoList.value
            val nextIndex = pageIndex + 1
            
            if (autoPlayNextEnabled.value && nextIndex < videos.size) {
                // 自动播放下一个视频
                updateCurrentIndex(nextIndex)
            } else {
                // 到达列表末尾或自动播放关闭，重播当前视频
                mediaPlayerService.getPlayerByIndex(pageIndex)?.let { player ->
                    player.seekTo(0)
                    player.play()
                }
            }
        }
    }

    /**
     * 初始化播放列表
     */
    fun initializePlaylist(videoList: List<VideoModel>, startIndex: Int) {
        viewModelScope.launch {
            if (videoList.isNotEmpty() && startIndex >= 0 && startIndex < videoList.size) {
                preparePlayerEnvironment()
                updateVideoData(videoList, startIndex)
                mediaPlayerService.initializePlayers(videoList, startIndex)
                updateCurrentVideoState(videoList[startIndex])
                startPlayback(startIndex)
            }
        }
    }

    /**
     * 准备播放器环境
     */
    private fun preparePlayerEnvironment() {
        // 清理播放器池，移除其他页面（如主页）留下的播放器状态
        mediaPlayerService.clearPlayerPool()
    }

    /**
     * 更新视频数据
     */
    private fun updateVideoData(videoList: List<VideoModel>, startIndex: Int) {
        _videoList.value = videoList
        _currentIndex.value = startIndex
    }

    /**
     * 更新当前视频状态（收藏状态和历史记录）
     */
    private fun updateCurrentVideoState(video: VideoModel) {
        updateCurrentVideoFavoriteStatus(video.video_id)
        addToHistory(video)
    }

    /**
     * 开始播放
     */
    private fun startPlayback(startIndex: Int) {
        mediaPlayerService.play(startIndex)
    }

    /**
     * 更新当前播放索引
     */
    fun updateCurrentIndex(index: Int) {
        val videos = _videoList.value
        if (isValidIndexChange(videos, index)) {
            performIndexChange(videos, index)
        }
    }

    /**
     * 验证索引变更是否有效
     */
    private fun isValidIndexChange(videos: List<VideoModel>, newIndex: Int): Boolean {
        return newIndex >= 0 && 
               newIndex < videos.size && 
               newIndex != _currentIndex.value
    }

    /**
     * 执行索引变更
     */
    private fun performIndexChange(videos: List<VideoModel>, newIndex: Int) {
        val oldIndex = _currentIndex.value
        
        // 停止旧播放器
        mediaPlayerService.stopPlayerForPage(oldIndex)
        
        // 更新索引
        _currentIndex.value = newIndex
        
        // 更新当前视频状态
        updateCurrentVideoState(videos[newIndex])
        
        // 开始播放新视频
        startPlayerForIndex(videos, newIndex)
    }

    /**
     * 为指定索引启动播放器
     */
    private fun startPlayerForIndex(videos: List<VideoModel>, index: Int) {
        val player = mediaPlayerService.getPlayerByIndex(index)
        if (player != null) {
            // 如果播放器已存在，直接播放
            mediaPlayerService.play(index)
        } else {
            // 如果播放器不存在，只为当前视频创建播放器
            mediaPlayerService.getOrCreatePlayer(videos[index], index)
            mediaPlayerService.play(index)
        }
    }

    override fun onCleared() {
        super.onCleared()
        // 停止播放器
        mediaPlayerService.stopPlayerForPage(_currentIndex.value)
        // 恢复原始回调
        mediaPlayerService.onVideoEnded = originalOnVideoEnded
    }
}

/**
 * VideoPlayerViewModel的工厂类
 */
@UnstableApi
class VideoPlayerViewModelFactory(
    private val application: Application,
    private val initialIndex: Int
) : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(VideoPlayerViewModel::class.java)) {
            return VideoPlayerViewModel(application, initialIndex) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
} 